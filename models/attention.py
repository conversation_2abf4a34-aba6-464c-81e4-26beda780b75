import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from einops import rearrange, repeat
from typing import Optional

class SpatialAttention(nn.Module):
    """空间注意力机制"""
    
    def __init__(self, in_channels: int, reduction_ratio: int = 8):
        super().__init__()
        self.in_channels = in_channels
        
        # 通道压缩
        self.channel_compress = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction_ratio, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction_ratio, 1, 1),
            nn.Sigmoid()
        )
        
        # 空间压缩
        self.spatial_compress = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, padding=3),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 通道注意力
        avg_pool = F.adaptive_avg_pool2d(x, 1)
        max_pool = F.adaptive_max_pool2d(x, 1)
        
        channel_att = self.channel_compress(avg_pool + max_pool)
        x_channel = x * channel_att
        
        # 空间注意力
        avg_spatial = torch.mean(x_channel, dim=1, keepdim=True)
        max_spatial, _ = torch.max(x_channel, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_spatial, max_spatial], dim=1)
        
        spatial_att = self.spatial_compress(spatial_input)
        x_spatial = x_channel * spatial_att
        
        return x_spatial

class CrossModalAttention(nn.Module):
    """跨模态注意力机制：用于RGB和多光谱特征融合"""
    
    def __init__(self, rgb_channels: int, ms_channels: int, 
                 hidden_dim: int = 256, num_heads: int = 8):
        super().__init__()
        self.rgb_channels = rgb_channels
        self.ms_channels = ms_channels
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        # 特征投影
        self.rgb_proj = nn.Conv2d(rgb_channels, hidden_dim, 1)
        self.ms_proj = nn.Conv2d(ms_channels, hidden_dim, 1)
        
        # 多头注意力
        self.q_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.k_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.v_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        
        self.out_proj = nn.Conv2d(hidden_dim, hidden_dim, 1)
        self.norm1 = nn.LayerNorm(hidden_dim)
        self.norm2 = nn.LayerNorm(hidden_dim)
        
        # FFN
        self.ffn = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim * 4, 1),
            nn.GELU(),
            nn.Conv2d(hidden_dim * 4, hidden_dim, 1)
        )
        
    def forward(self, rgb_feat: torch.Tensor, ms_feat: torch.Tensor) -> torch.Tensor:
        B, _, H, W = rgb_feat.shape
        
        # 特征投影
        rgb_proj = self.rgb_proj(rgb_feat)
        ms_proj = self.ms_proj(ms_feat)
        
        # 准备查询、键、值
        q = self.q_proj(rgb_proj)  # RGB作为查询
        k = self.k_proj(ms_proj)   # 多光谱作为键
        v = self.v_proj(ms_proj)   # 多光谱作为值
        
        # 重塑为多头格式
        q = rearrange(q, 'b (h d) H W -> b h (H W) d', h=self.num_heads)
        k = rearrange(k, 'b (h d) H W -> b h (H W) d', h=self.num_heads)
        v = rearrange(v, 'b (h d) H W -> b h (H W) d', h=self.num_heads)
        
        # 注意力计算
        scale = self.head_dim ** -0.5
        attention = torch.matmul(q, k.transpose(-2, -1)) * scale
        attention = F.softmax(attention, dim=-1)
        
        out = torch.matmul(attention, v)
        out = rearrange(out, 'b h (H W) d -> b (h d) H W', H=H, W=W)
        
        # 输出投影和残差连接
        out = self.out_proj(out)
        out = out + rgb_proj  # 残差连接
        
        # 层归一化
        out_norm = self.norm1(out.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)
        
        # FFN
        ffn_out = self.ffn(out_norm)
        out_final = ffn_out + out_norm
        out_final = self.norm2(out_final.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)
        
        return out_final

class TemporalAttention(nn.Module):
    """时间注意力机制：处理时间序列Sentinel-2数据"""
    
    def __init__(self, feature_dim: int, num_heads: int = 8):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        
        self.q_linear = nn.Linear(feature_dim, feature_dim)
        self.k_linear = nn.Linear(feature_dim, feature_dim)
        self.v_linear = nn.Linear(feature_dim, feature_dim)
        self.out_linear = nn.Linear(feature_dim, feature_dim)
        
        self.dropout = nn.Dropout(0.1)
        self.norm = nn.LayerNorm(feature_dim)
        
    def forward(self, temporal_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            temporal_features: [B, T, C, H, W] 时间序列特征
        Returns:
            fused_features: [B, C, H, W] 融合后的特征
        """
        B, T, C, H, W = temporal_features.shape
        
        # 重塑为 [B, H, W, T, C]
        x = temporal_features.permute(0, 3, 4, 1, 2)
        x = x.view(B * H * W, T, C)
        
        # 多头自注意力
        q = self.q_linear(x).view(B * H * W, T, self.num_heads, self.head_dim)
        k = self.k_linear(x).view(B * H * W, T, self.num_heads, self.head_dim)
        v = self.v_linear(x).view(B * H * W, T, self.num_heads, self.head_dim)
        
        q = q.transpose(1, 2)  # [B*H*W, num_heads, T, head_dim]
        k = k.transpose(1, 2)
        v = v.transpose(1, 2)
        
        # 注意力分数
        scale = self.head_dim ** -0.5
        attention = torch.matmul(q, k.transpose(-2, -1)) * scale
        attention = F.softmax(attention, dim=-1)
        attention = self.dropout(attention)
        
        # 应用注意力
        out = torch.matmul(attention, v)
        out = out.transpose(1, 2).contiguous()
        out = out.view(B * H * W, T, C)
        
        # 输出投影
        out = self.out_linear(out)
        
        # 残差连接和归一化
        out = self.norm(out + x)
        
        # 时间加权平均
        temporal_weights = F.softmax(torch.mean(out, dim=-1), dim=-1)  # [B*H*W, T]
        fused = torch.sum(out * temporal_weights.unsqueeze(-1), dim=1)  # [B*H*W, C]
        
        # 重塑回原始空间维度
        fused = fused.view(B, H, W, C).permute(0, 3, 1, 2)  # [B, C, H, W]
        
        return fused

class GradientGuidedAttention(nn.Module):
    """梯度引导注意力机制"""
    
    def __init__(self, in_channels: int):
        super().__init__()
        self.in_channels = in_channels
        
        # Sobel算子用于梯度计算
        self.register_buffer('sobel_x', torch.tensor([
            [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
        ], dtype=torch.float32).unsqueeze(0))
        
        self.register_buffer('sobel_y', torch.tensor([
            [[-1, -2, -1], [0, 0, 0], [1, 2, 1]]
        ], dtype=torch.float32).unsqueeze(0))
        
        # 梯度特征处理
        self.gradient_conv = nn.Sequential(
            nn.Conv2d(2, 32, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 1, 3, padding=1),
            nn.Sigmoid()
        )
        
        # 特征融合
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(in_channels + 1, in_channels, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels, in_channels, 3, padding=1)
        )
        
    def compute_gradient(self, x: torch.Tensor) -> torch.Tensor:
        """计算梯度幅值"""
        # 转换为灰度（如果是多通道）
        if x.size(1) > 1:
            gray = torch.mean(x, dim=1, keepdim=True)
        else:
            gray = x
        
        # 计算梯度
        grad_x = F.conv2d(gray, self.sobel_x, padding=1)
        grad_y = F.conv2d(gray, self.sobel_y, padding=1)
        
        # 梯度幅值
        gradient_magnitude = torch.sqrt(grad_x ** 2 + grad_y ** 2 + 1e-8)
        
        return torch.cat([grad_x, grad_y], dim=1)
    
    def forward(self, features: torch.Tensor, reference: torch.Tensor) -> torch.Tensor:
        """
        Args:
            features: 待处理的特征
            reference: 参考图像（用于计算梯度）
        """
        # 计算参考图像的梯度
        gradient = self.compute_gradient(reference)
        gradient_attention = self.gradient_conv(gradient)
        
        # 应用梯度注意力
        attended_features = features * gradient_attention
        
        # 特征融合
        combined = torch.cat([attended_features, gradient_attention], dim=1)
        output = self.fusion_conv(combined)
        
        return output + features  # 残差连接

class MultiScaleAttention(nn.Module):
    """多尺度注意力机制"""
    
    def __init__(self, in_channels: int, scales: list = [1, 2, 4]):
        super().__init__()
        self.scales = scales
        self.in_channels = in_channels
        
        # 多尺度卷积
        self.scale_convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_channels, in_channels // len(scales), 3, 
                         padding=scale, dilation=scale),
                nn.ReLU(inplace=True)
            ) for scale in scales
        ])
        
        # 注意力权重生成
        self.attention_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // 4, len(scales), 1),
            nn.Softmax(dim=1)
        )
        
        # 输出投影
        self.output_conv = nn.Conv2d(in_channels, in_channels, 1)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 多尺度特征提取
        scale_features = []
        for scale_conv in self.scale_convs:
            scale_features.append(scale_conv(x))
        
        # 拼接多尺度特征
        multi_scale_feat = torch.cat(scale_features, dim=1)
        
        # 生成注意力权重
        attention_weights = self.attention_conv(x)  # [B, num_scales, H, W]
        
        # 加权融合
        weighted_features = []
        for i, feat in enumerate(scale_features):
            weight = attention_weights[:, i:i+1, :, :]  # [B, 1, H, W]
            weighted_features.append(feat * weight)
        
        fused_features = torch.cat(weighted_features, dim=1)
        output = self.output_conv(fused_features)
        
        return output + x  # 残差连接