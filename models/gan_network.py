import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple
from .attention import (
    SpatialAttention, CrossModalAttention, TemporalAttention,
    GradientGuidedAttention, MultiScaleAttention
)

class ResidualBlock(nn.Module):
    """残差块"""
    
    def __init__(self, in_channels: int, out_channels: int, stride: int = 1):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, stride, 1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, 1, 1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, stride),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        return F.relu(out)

class UpsampleBlock(nn.Module):
    """上采样块"""
    
    def __init__(self, in_channels: int, out_channels: int, scale_factor: int = 2):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, out_channels * (scale_factor ** 2), 3, 1, 1)
        self.pixel_shuffle = nn.PixelShuffle(scale_factor)
        self.bn = nn.BatchNorm2d(out_channels)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv(x)
        x = self.pixel_shuffle(x)
        x = self.bn(x)
        return F.relu(x)

class FeatureExtractor(nn.Module):
    """特征提取器"""
    
    def __init__(self, in_channels: int, base_channels: int = 64):
        super().__init__()
        
        # 初始卷积
        self.initial_conv = nn.Sequential(
            nn.Conv2d(in_channels, base_channels, 7, 1, 3),
            nn.BatchNorm2d(base_channels),
            nn.ReLU(inplace=True)
        )
        
        # 多尺度特征提取
        self.layer1 = self._make_layer(base_channels, base_channels, 2)
        self.layer2 = self._make_layer(base_channels, base_channels * 2, 2, stride=2)
        self.layer3 = self._make_layer(base_channels * 2, base_channels * 4, 2, stride=2)
        self.layer4 = self._make_layer(base_channels * 4, base_channels * 8, 2, stride=2)
        
        # 注意力机制
        self.spatial_att1 = SpatialAttention(base_channels)
        self.spatial_att2 = SpatialAttention(base_channels * 2)
        self.spatial_att3 = SpatialAttention(base_channels * 4)
        self.spatial_att4 = SpatialAttention(base_channels * 8)
        
    def _make_layer(self, in_channels: int, out_channels: int, 
                   num_blocks: int, stride: int = 1) -> nn.Sequential:
        layers = []
        layers.append(ResidualBlock(in_channels, out_channels, stride))
        for _ in range(1, num_blocks):
            layers.append(ResidualBlock(out_channels, out_channels))
        return nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        x = self.initial_conv(x)
        
        # 多尺度特征提取
        feat1 = self.spatial_att1(self.layer1(x))
        feat2 = self.spatial_att2(self.layer2(feat1))
        feat3 = self.spatial_att3(self.layer3(feat2))
        feat4 = self.spatial_att4(self.layer4(feat3))
        
        return [feat1, feat2, feat3, feat4]

class FusionGenerator(nn.Module):
    """时空融合生成器"""
    
    def __init__(self, rgb_channels: int = 3, ms_channels: int = 4, 
                 output_channels: int = 4, base_channels: int = 64):
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.ms_channels = ms_channels
        self.output_channels = output_channels
        
        # 特征提取器
        self.rgb_extractor = FeatureExtractor(rgb_channels, base_channels)
        self.ms_extractor = FeatureExtractor(ms_channels, base_channels)
        
        # 跨模态注意力
        self.cross_attention1 = CrossModalAttention(base_channels, base_channels)
        self.cross_attention2 = CrossModalAttention(base_channels * 2, base_channels * 2)
        self.cross_attention3 = CrossModalAttention(base_channels * 4, base_channels * 4)
        self.cross_attention4 = CrossModalAttention(base_channels * 8, base_channels * 8)
        
        # 时间注意力（用于处理多时相Sentinel-2数据）
        self.temporal_attention = TemporalAttention(base_channels * 8)
        
        # 梯度引导注意力
        self.gradient_attention1 = GradientGuidedAttention(base_channels)
        self.gradient_attention2 = GradientGuidedAttention(base_channels * 2)
        self.gradient_attention3 = GradientGuidedAttention(base_channels * 4)
        self.gradient_attention4 = GradientGuidedAttention(base_channels * 8)
        
        # 多尺度注意力
        self.multiscale_attention = MultiScaleAttention(base_channels * 8)
        
        # 解码器
        self.decoder4 = UpsampleBlock(base_channels * 8, base_channels * 4)
        self.decoder3 = UpsampleBlock(base_channels * 8, base_channels * 2)  # 8 = 4 + 4 (skip connection)
        self.decoder2 = UpsampleBlock(base_channels * 4, base_channels)      # 4 = 2 + 2
        self.decoder1 = UpsampleBlock(base_channels * 2, base_channels // 2) # 2 = 1 + 1
        
        # 最终输出层
        self.final_conv = nn.Sequential(
            nn.Conv2d(base_channels // 2, output_channels, 3, 1, 1),
            nn.Tanh()  # 输出范围 [-1, 1]
        )
        
        # 超分辨率分支
        self.sr_branch = nn.Sequential(
            nn.Conv2d(base_channels * 8, base_channels * 4, 3, 1, 1),
            nn.ReLU(inplace=True),
            UpsampleBlock(base_channels * 4, base_channels * 2, scale_factor=4),
            nn.Conv2d(base_channels * 2, output_channels, 3, 1, 1),
            nn.Tanh()
        )
        
    def forward(self, rgb_lr: torch.Tensor, ms_hr: torch.Tensor, 
                reference_rgb: torch.Tensor = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            rgb_lr: 低分辨率RGB图像
            ms_hr: 高分辨率多光谱图像
            reference_rgb: 参考RGB图像（用于梯度引导）
        Returns:
            融合后的高分辨率多光谱图像, 超分辨率RGB图像
        """
        # 特征提取
        rgb_features = self.rgb_extractor(rgb_lr)
        ms_features = self.ms_extractor(ms_hr)
        
        # 跨模态特征融合
        fused_feat1 = self.cross_attention1(rgb_features[0], ms_features[0])
        fused_feat2 = self.cross_attention2(rgb_features[1], ms_features[1])
        fused_feat3 = self.cross_attention3(rgb_features[2], ms_features[2])
        fused_feat4 = self.cross_attention4(rgb_features[3], ms_features[3])
        
        # 梯度引导（如果提供参考图像）
        if reference_rgb is not None:
            fused_feat1 = self.gradient_attention1(fused_feat1, reference_rgb)
            fused_feat2 = self.gradient_attention2(fused_feat2, reference_rgb)
            fused_feat3 = self.gradient_attention3(fused_feat3, reference_rgb)
            fused_feat4 = self.gradient_attention4(fused_feat4, reference_rgb)
        
        # 多尺度注意力增强
        enhanced_feat4 = self.multiscale_attention(fused_feat4)
        
        # 解码器 - 渐进式上采样
        up4 = self.decoder4(enhanced_feat4)
        up3 = self.decoder3(torch.cat([up4, fused_feat3], dim=1))
        up2 = self.decoder2(torch.cat([up3, fused_feat2], dim=1))
        up1 = self.decoder1(torch.cat([up2, fused_feat1], dim=1))
        
        # 最终输出
        fused_output = self.final_conv(up1)
        
        # 超分辨率分支
        sr_output = self.sr_branch(enhanced_feat4)
        
        return fused_output, sr_output

class PatchDiscriminator(nn.Module):
    """PatchGAN判别器"""
    
    def __init__(self, in_channels: int = 4, base_channels: int = 64):
        super().__init__()
        
        # 使用谱归一化提高训练稳定性
        self.conv_layers = nn.Sequential(
            # 输入: [B, in_channels, H, W]
            nn.utils.spectral_norm(
                nn.Conv2d(in_channels, base_channels, 4, 2, 1)
            ),
            nn.LeakyReLU(0.2, inplace=True),
            
            # [B, base_channels, H/2, W/2]
            nn.utils.spectral_norm(
                nn.Conv2d(base_channels, base_channels * 2, 4, 2, 1)
            ),
            nn.BatchNorm2d(base_channels * 2),
            nn.LeakyReLU(0.2, inplace=True),
            
            # [B, base_channels*2, H/4, W/4]
            nn.utils.spectral_norm(
                nn.Conv2d(base_channels * 2, base_channels * 4, 4, 2, 1)
            ),
            nn.BatchNorm2d(base_channels * 4),
            nn.LeakyReLU(0.2, inplace=True),
            
            # [B, base_channels*4, H/8, W/8]
            nn.utils.spectral_norm(
                nn.Conv2d(base_channels * 4, base_channels * 8, 4, 1, 1)
            ),
            nn.BatchNorm2d(base_channels * 8),
            nn.LeakyReLU(0.2, inplace=True),
            
            # [B, base_channels*8, H/8-1, W/8-1]
            nn.utils.spectral_norm(
                nn.Conv2d(base_channels * 8, 1, 4, 1, 1)
            )
            # 输出: [B, 1, H/8-2, W/8-2] - PatchGAN输出
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.conv_layers(x)

class MultiScaleDiscriminator(nn.Module):
    """多尺度判别器"""
    
    def __init__(self, in_channels: int = 4, num_scales: int = 3):
        super().__init__()
        self.num_scales = num_scales
        
        # 多个尺度的判别器
        self.discriminators = nn.ModuleList([
            PatchDiscriminator(in_channels) for _ in range(num_scales)
        ])
        
        # 下采样层
        self.downsample = nn.AvgPool2d(3, stride=2, padding=1)
        
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        outputs = []
        
        for i, discriminator in enumerate(self.discriminators):
            if i == 0:
                # 原始尺度
                output = discriminator(x)
            else:
                # 下采样到不同尺度
                x_downsampled = x
                for _ in range(i):
                    x_downsampled = self.downsample(x_downsampled)
                output = discriminator(x_downsampled)
            
            outputs.append(output)
        
        return outputs

class TemporalDiscriminator(nn.Module):
    """时间判别器：确保时间一致性"""
    
    def __init__(self, in_channels: int = 4, base_channels: int = 64):
        super().__init__()
        
        # 3D卷积处理时间序列
        self.conv3d_layers = nn.Sequential(
            nn.Conv3d(in_channels, base_channels, (3, 4, 4), (1, 2, 2), (1, 1, 1)),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Conv3d(base_channels, base_channels * 2, (3, 4, 4), (1, 2, 2), (1, 1, 1)),
            nn.BatchNorm3d(base_channels * 2),
            nn.LeakyReLU(0.2, inplace=True),
            
            nn.Conv3d(base_channels * 2, base_channels * 4, (3, 4, 4), (1, 2, 2), (1, 1, 1)),
            nn.BatchNorm3d(base_channels * 4),
            nn.LeakyReLU(0.2, inplace=True)
        )
        
        # 全局平均池化和分类器
        self.classifier = nn.Sequential(
            nn.AdaptiveAvgPool3d(1),
            nn.Flatten(),
            nn.Linear(base_channels * 4, 1)
        )
        
    def forward(self, temporal_sequence: torch.Tensor) -> torch.Tensor:
        """
        Args:
            temporal_sequence: [B, T, C, H, W] 时间序列
        Returns:
            时间一致性分数
        """
        # 转换为 [B, C, T, H, W] 格式用于3D卷积
        x = temporal_sequence.permute(0, 2, 1, 3, 4)
        
        x = self.conv3d_layers(x)
        output = self.classifier(x)
        
        return output