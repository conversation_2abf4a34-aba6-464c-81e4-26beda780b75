import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from typing import Tuple, Optional, List, Dict
from functools import partial
from .attention import SpatialAttention, CrossModalAttention, TemporalAttention

class ScoreNetwork(nn.Module):
    """Score网络：预测噪声分布的梯度（score function）"""
    
    def __init__(self, 
                 rgb_channels: int = 3, 
                 ms_channels: int = 4, 
                 base_channels: int = 64,
                 time_embed_dim: int = 128):
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.ms_channels = ms_channels
        self.base_channels = base_channels
        self.time_embed_dim = time_embed_dim
        
        # 时间嵌入
        self.time_embedding = nn.Sequential(
            nn.Linear(1, time_embed_dim),
            nn.SiLU(),
            nn.Linear(time_embed_dim, time_embed_dim)
        )
        
        # RGB和多光谱特征编码器
        self.rgb_encoder = self._build_encoder(rgb_channels, base_channels)
        self.ms_encoder = self._build_encoder(ms_channels, base_channels)
        
        # 跨模态注意力融合
        self.cross_attention = CrossModalAttention(
            base_channels * 8, base_channels * 8, 
            hidden_dim=base_channels * 4, num_heads=8
        )
        
        # 时间调制层
        self.time_modulation = nn.ModuleList([
            nn.Sequential(
                nn.SiLU(),
                nn.Linear(time_embed_dim, base_channels * (2 ** i))
            ) for i in range(4)
        ])
        
        # Score预测解码器
        self.decoder = self._build_decoder(base_channels, ms_channels)
        
    def _build_encoder(self, in_channels: int, base_channels: int) -> nn.ModuleList:
        """构建编码器"""
        layers = nn.ModuleList()
        
        # 初始卷积
        layers.append(nn.Sequential(
            nn.Conv2d(in_channels, base_channels, 3, 1, 1),
            nn.GroupNorm(8, base_channels),
            nn.SiLU()
        ))
        
        # 下采样层
        channels = base_channels
        for i in range(3):
            layers.append(nn.Sequential(
                nn.Conv2d(channels, channels * 2, 3, 2, 1),
                nn.GroupNorm(8, channels * 2),
                nn.SiLU(),
                ResNetBlock(channels * 2, channels * 2),
                ResNetBlock(channels * 2, channels * 2)
            ))
            channels *= 2
            
        return layers
        
    def _build_decoder(self, base_channels: int, out_channels: int) -> nn.ModuleList:
        """构建解码器"""
        layers = nn.ModuleList()
        
        # 上采样层
        channels = base_channels * 8
        for i in range(3):
            layers.append(nn.Sequential(
                nn.ConvTranspose2d(channels, channels // 2, 4, 2, 1),
                nn.GroupNorm(8, channels // 2),
                nn.SiLU(),
                ResNetBlock(channels // 2, channels // 2),
                ResNetBlock(channels // 2, channels // 2)
            ))
            channels //= 2
            
        # 最终输出层
        layers.append(nn.Conv2d(base_channels, out_channels, 3, 1, 1))
        
        return layers
    
    def forward(self, 
                rgb_noisy: torch.Tensor, 
                ms_noisy: torch.Tensor, 
                time: torch.Tensor) -> torch.Tensor:
        """
        Args:
            rgb_noisy: 带噪声的RGB图像 [B, 3, H, W]
            ms_noisy: 带噪声的多光谱图像 [B, 4, H, W]  
            time: 时间步 [B, 1]
        Returns:
            预测的score [B, 4, H, W]
        """
        # 时间嵌入
        time_emb = self.time_embedding(time)  # [B, time_embed_dim]
        
        # RGB特征编码
        rgb_feat = rgb_noisy
        rgb_features = []
        for i, layer in enumerate(self.rgb_encoder):
            rgb_feat = layer(rgb_feat)
            if i > 0:  # 跳过初始卷积层
                # 时间调制
                time_mod = self.time_modulation[i-1](time_emb)
                time_mod = time_mod.view(time_mod.shape[0], -1, 1, 1)
                rgb_feat = rgb_feat * (1 + time_mod)
            rgb_features.append(rgb_feat)
            
        # 多光谱特征编码
        ms_feat = ms_noisy
        ms_features = []
        for i, layer in enumerate(self.ms_encoder):
            ms_feat = layer(ms_feat)
            if i > 0:
                time_mod = self.time_modulation[i-1](time_emb)
                time_mod = time_mod.view(time_mod.shape[0], -1, 1, 1)
                ms_feat = ms_feat * (1 + time_mod)
            ms_features.append(ms_feat)
        
        # 跨模态特征融合
        fused_feat = self.cross_attention(rgb_features[-1], ms_features[-1])
        
        # Score预测解码
        x = fused_feat
        for layer in self.decoder[:-1]:
            x = layer(x)
            
        # 最终score输出
        score = self.decoder[-1](x)
        
        return score

class ResNetBlock(nn.Module):
    """ResNet残差块"""
    
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, 1, 1)
        self.norm1 = nn.GroupNorm(8, out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, 1, 1)
        self.norm2 = nn.GroupNorm(8, out_channels)
        
        self.skip = nn.Identity() if in_channels == out_channels else \
                    nn.Conv2d(in_channels, out_channels, 1)
                    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        h = F.silu(self.norm1(self.conv1(x)))
        h = self.norm2(self.conv2(h))
        return F.silu(h + self.skip(x))

class VarianceExplodingSDE:
    """Variance Exploding SDE"""
    
    def __init__(self, 
                 sigma_min: float = 0.01, 
                 sigma_max: float = 50.0,
                 num_scales: int = 1000):
        self.sigma_min = sigma_min
        self.sigma_max = sigma_max
        self.num_scales = num_scales
        
    def marginal_prob(self, x: torch.Tensor, t: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """边际概率分布参数"""
        sigma = self.sigma_min * (self.sigma_max / self.sigma_min) ** t
        mean = x
        std = sigma
        return mean, std
        
    def prior_sampling(self, shape: Tuple[int, ...]) -> torch.Tensor:
        """从先验分布采样"""
        return torch.randn(*shape) * self.sigma_max
        
    def sde(self, x: torch.Tensor, t: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """SDE系数"""
        sigma = self.sigma_min * (self.sigma_max / self.sigma_min) ** t
        drift = torch.zeros_like(x)
        diffusion = sigma * math.sqrt(2.0 * math.log(self.sigma_max / self.sigma_min))
        return drift, diffusion

class ScoreDiffusionSpatiotemporalFusion(nn.Module):
    """基于Score Diffusion SDE的时空融合模型"""
    
    def __init__(self,
                 rgb_channels: int = 3,
                 ms_channels: int = 4,
                 base_channels: int = 64,
                 num_timesteps: int = 1000,
                 sigma_min: float = 0.01,
                 sigma_max: float = 50.0):
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.ms_channels = ms_channels
        self.num_timesteps = num_timesteps
        
        # Score网络
        self.score_net = ScoreNetwork(
            rgb_channels=rgb_channels,
            ms_channels=ms_channels,
            base_channels=base_channels
        )
        
        # SDE定义
        self.sde = VarianceExplodingSDE(sigma_min, sigma_max, num_timesteps)
        
        # 时间采样
        self.register_buffer('timesteps', torch.linspace(0, 1, num_timesteps))
        
    def get_score_loss(self, 
                      rgb_clean: torch.Tensor, 
                      ms_clean: torch.Tensor) -> torch.Tensor:
        """计算score matching损失"""
        batch_size = rgb_clean.shape[0]
        device = rgb_clean.device
        
        # 随机采样时间步
        t = torch.rand(batch_size, 1, device=device)
        
        # 获取边际分布参数
        _, std = self.sde.marginal_prob(ms_clean, t)
        
        # 添加噪声
        noise = torch.randn_like(ms_clean)
        ms_noisy = ms_clean + std.view(-1, 1, 1, 1) * noise
        
        # RGB也添加相应噪声（保持一致性）
        rgb_noise = torch.randn_like(rgb_clean)
        rgb_noisy = rgb_clean + std.view(-1, 1, 1, 1) * rgb_noise
        
        # 预测score
        predicted_score = self.score_net(rgb_noisy, ms_noisy, t)
        
        # 真实score = -noise / std^2
        target_score = -noise / (std.view(-1, 1, 1, 1) ** 2)
        
        # Score matching损失
        loss = F.mse_loss(predicted_score, target_score)
        
        return loss
    
    def euler_maruyama_sampler(self, 
                              rgb_lr: torch.Tensor,
                              num_steps: int = 500) -> torch.Tensor:
        """Euler-Maruyama采样器"""
        batch_size = rgb_lr.shape[0]
        device = rgb_lr.device
        
        # 从先验分布初始化
        x = self.sde.prior_sampling((batch_size, self.ms_channels, 
                                   rgb_lr.shape[2], rgb_lr.shape[3])).to(device)
        
        # 时间步长
        dt = 1.0 / num_steps
        
        for i in range(num_steps):
            t = torch.ones(batch_size, 1, device=device) * (1 - i * dt)
            
            # 预测score
            with torch.no_grad():
                score = self.score_net(rgb_lr, x, t)
            
            # SDE系数
            drift, diffusion = self.sde.sde(x, t)
            
            # Euler-Maruyama更新
            drift_term = (drift - diffusion ** 2 * score) * dt
            diffusion_term = diffusion * math.sqrt(dt) * torch.randn_like(x)
            
            x = x + drift_term + diffusion_term
            
        return x
    
    def predictor_corrector_sampler(self,
                                  rgb_lr: torch.Tensor,
                                  num_steps: int = 500,
                                  corrector_steps: int = 1,
                                  snr: float = 0.16) -> torch.Tensor:
        """Predictor-Corrector采样器"""
        batch_size = rgb_lr.shape[0]
        device = rgb_lr.device
        
        # 初始化
        x = self.sde.prior_sampling((batch_size, self.ms_channels,
                                   rgb_lr.shape[2], rgb_lr.shape[3])).to(device)
        
        dt = 1.0 / num_steps
        
        for i in range(num_steps):
            t = torch.ones(batch_size, 1, device=device) * (1 - i * dt)
            
            # Predictor step (Euler-Maruyama)
            with torch.no_grad():
                score = self.score_net(rgb_lr, x, t)
            
            drift, diffusion = self.sde.sde(x, t)
            x_mean = x + (drift - diffusion ** 2 * score) * dt
            x = x_mean + diffusion * math.sqrt(dt) * torch.randn_like(x)
            
            # Corrector steps (Langevin MCMC)
            for _ in range(corrector_steps):
                with torch.no_grad():
                    score = self.score_net(rgb_lr, x, t)
                
                noise = torch.randn_like(x)
                grad_norm = torch.norm(score.view(batch_size, -1), dim=-1).mean()
                noise_norm = torch.norm(noise.view(batch_size, -1), dim=-1).mean()
                
                step_size = (snr * noise_norm / grad_norm) ** 2 * 2
                x_mean = x + step_size * score
                x = x_mean + torch.sqrt(2 * step_size) * noise
                
        return x
    
    def forward(self, 
               rgb_lr: torch.Tensor, 
               ms_hr: Optional[torch.Tensor] = None,
               sampling_method: str = "predictor_corrector") -> torch.Tensor:
        """
        前向传播
        Args:
            rgb_lr: 低分辨率RGB图像
            ms_hr: 高分辨率多光谱图像（训练时使用）
            sampling_method: 采样方法 ("euler_maruyama" 或 "predictor_corrector")
        """
        if self.training and ms_hr is not None:
            # 训练模式：返回score matching损失
            return self.get_score_loss(rgb_lr, ms_hr)
        else:
            # 推理模式：生成融合结果
            if sampling_method == "euler_maruyama":
                return self.euler_maruyama_sampler(rgb_lr)
            else:
                return self.predictor_corrector_sampler(rgb_lr)

class TemporalScoreDiffusion(nn.Module):
    """时间维度的Score Diffusion模块"""
    
    def __init__(self, feature_dim: int, num_timesteps: int = 8):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_timesteps = num_timesteps
        
        # 时间注意力
        self.temporal_attention = TemporalAttention(feature_dim)
        
        # 时间Score网络
        self.temporal_score_net = nn.Sequential(
            nn.Conv3d(feature_dim, feature_dim * 2, (3, 3, 3), padding=1),
            nn.GroupNorm(8, feature_dim * 2),
            nn.SiLU(),
            nn.Conv3d(feature_dim * 2, feature_dim, (3, 3, 3), padding=1),
            nn.GroupNorm(8, feature_dim),
            nn.SiLU(),
            nn.Conv3d(feature_dim, feature_dim, (1, 3, 3), padding=(0, 1, 1))
        )
        
    def forward(self, temporal_features: torch.Tensor) -> torch.Tensor:
        """
        Args:
            temporal_features: [B, T, C, H, W] 时间序列特征
        Returns:
            融合后的特征: [B, C, H, W]
        """
        B, T, C, H, W = temporal_features.shape
        
        # 重新排列为 [B, C, T, H, W] 以适应3D卷积
        x = temporal_features.permute(0, 2, 1, 3, 4)
        
        # 时间维度的score预测
        temporal_score = self.temporal_score_net(x)
        
        # 转换回 [B, T, C, H, W]
        temporal_score = temporal_score.permute(0, 2, 1, 3, 4)
        
        # 应用时间注意力进行融合
        fused_features = self.temporal_attention(temporal_score)
        
        return fused_features