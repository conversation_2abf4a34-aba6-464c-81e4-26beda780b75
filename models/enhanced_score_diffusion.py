import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
from .score_sde_fusion import ScoreDiffusionSpatiotemporalFusion, TemporalScoreDiffusion
from .score_sde_losses import ScoreDiffusionComprehensiveLoss
from .attention import TemporalAttention, SpatialAttention, CrossModalAttention
from .gan_network import FusionGenerator

class EnhancedScoreDiffusionFusion(nn.Module):
    """增强的Score Diffusion SDE时空融合系统"""
    
    def __init__(self,
                 rgb_channels: int = 3,
                 ms_channels: int = 4,
                 base_channels: int = 64,
                 num_timesteps: int = 1000,
                 use_temporal_diffusion: bool = True,
                 use_hybrid_architecture: bool = True):
        super().__init__()
        
        self.rgb_channels = rgb_channels
        self.ms_channels = ms_channels
        self.use_temporal_diffusion = use_temporal_diffusion
        self.use_hybrid_architecture = use_hybrid_architecture
        
        # 主要的Score Diffusion模块
        self.score_diffusion = ScoreDiffusionSpatiotemporalFusion(
            rgb_channels=rgb_channels,
            ms_channels=ms_channels,
            base_channels=base_channels,
            num_timesteps=num_timesteps
        )
        
        # 时间维度的Score Diffusion（如果启用）
        if use_temporal_diffusion:
            self.temporal_diffusion = TemporalScoreDiffusion(
                feature_dim=base_channels * 8,
                num_timesteps=8
            )
        
        # 混合架构：结合传统GAN和Score Diffusion（如果启用）
        if use_hybrid_architecture:
            self.gan_generator = FusionGenerator(
                rgb_channels=rgb_channels,
                ms_channels=ms_channels,
                output_channels=ms_channels,
                base_channels=base_channels // 2  # 减少参数避免冲突
            )
            
            # 特征融合模块
            self.feature_fusion = nn.Sequential(
                nn.Conv2d(ms_channels * 2, ms_channels, 3, 1, 1),
                nn.BatchNorm2d(ms_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(ms_channels, ms_channels, 1)
            )
        
        # 多尺度注意力增强
        self.multiscale_attention = nn.ModuleList([
            SpatialAttention(ms_channels),
            SpatialAttention(ms_channels),
            SpatialAttention(ms_channels)
        ])
        
        # 自适应权重融合
        self.adaptive_fusion = nn.Sequential(
            nn.Conv2d(ms_channels * 3 if use_hybrid_architecture else ms_channels * 2, 
                     ms_channels, 3, 1, 1),
            nn.BatchNorm2d(ms_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(ms_channels, ms_channels, 1),
            nn.Sigmoid()
        )
        
        # 损失函数
        self.criterion = ScoreDiffusionComprehensiveLoss(
            adaptive_weights=True
        )
        
    def extract_multiscale_features(self, x: torch.Tensor) -> List[torch.Tensor]:
        """提取多尺度特征"""
        features = []
        
        # 原始尺度
        features.append(x)
        
        # 下采样特征
        x_half = F.interpolate(x, scale_factor=0.5, mode='bilinear', align_corners=False)
        features.append(F.interpolate(x_half, size=x.shape[-2:], 
                                    mode='bilinear', align_corners=False))
        
        # 进一步下采样
        x_quarter = F.interpolate(x, scale_factor=0.25, mode='bilinear', align_corners=False)
        features.append(F.interpolate(x_quarter, size=x.shape[-2:], 
                                    mode='bilinear', align_corners=False))
        
        return features
    
    def apply_multiscale_attention(self, features: List[torch.Tensor]) -> torch.Tensor:
        """应用多尺度注意力"""
        attended_features = []
        
        for i, (feat, attention) in enumerate(zip(features, self.multiscale_attention)):
            attended_feat = attention(feat)
            attended_features.append(attended_feat)
            
        return torch.cat(attended_features, dim=1)
    
    def forward(self,
               rgb_lr: torch.Tensor,
               ms_hr: Optional[torch.Tensor] = None,
               temporal_ms: Optional[torch.Tensor] = None,
               mode: str = "train") -> Dict[str, torch.Tensor]:
        """
        前向传播
        Args:
            rgb_lr: 低分辨率RGB图像 [B, 3, H, W]
            ms_hr: 高分辨率多光谱图像 [B, 4, H, W] (训练时使用)
            temporal_ms: 时间序列多光谱数据 [B, T, 4, H, W] (可选)
            mode: "train" 或 "inference"
        """
        results = {}
        
        if mode == "train" and ms_hr is not None:
            # 训练模式
            
            # 1. Score Diffusion损失
            score_loss = self.score_diffusion(rgb_lr, ms_hr)
            results['score_loss'] = score_loss
            
            # 2. 如果使用混合架构，计算GAN生成结果
            if self.use_hybrid_architecture:
                gan_output, sr_output = self.gan_generator(rgb_lr, ms_hr)
                results['gan_output'] = gan_output
                results['sr_output'] = sr_output
                
                # 特征融合
                diffusion_sample = self.score_diffusion.euler_maruyama_sampler(rgb_lr, num_steps=100)
                fused_output = self.feature_fusion(torch.cat([gan_output, diffusion_sample], dim=1))
                results['fused_output'] = fused_output
            
            # 3. 时间维度处理
            if self.use_temporal_diffusion and temporal_ms is not None:
                # 提取时间特征（这里简化处理）
                B, T, C, H, W = temporal_ms.shape
                temporal_features = temporal_ms.view(B, T, C, H, W)
                
                # 时间融合
                temporal_fused = self.temporal_diffusion(temporal_features)
                results['temporal_fused'] = temporal_fused
            
        else:
            # 推理模式
            
            # 1. Score Diffusion采样
            diffusion_output = self.score_diffusion(rgb_lr, sampling_method="predictor_corrector")
            
            # 2. 混合架构增强（如果启用）
            if self.use_hybrid_architecture:
                # 使用预训练的GAN生成器生成初始结果
                with torch.no_grad():
                    gan_output, _ = self.gan_generator(rgb_lr, 
                                                    F.interpolate(rgb_lr, size=diffusion_output.shape[-2:], 
                                                                mode='bilinear', align_corners=False).repeat(1, self.ms_channels//self.rgb_channels + 1, 1, 1)[:, :self.ms_channels])
                
                # 特征融合
                fused_features = self.feature_fusion(torch.cat([gan_output, diffusion_output], dim=1))
                
                # 多尺度处理
                multiscale_features = self.extract_multiscale_features(fused_features)
                attended_features = self.apply_multiscale_attention(multiscale_features)
                
                # 自适应融合权重
                fusion_weights = self.adaptive_fusion(attended_features)
                
                # 最终输出
                final_output = fused_features * fusion_weights + diffusion_output * (1 - fusion_weights)
                
                results['output'] = final_output
                results['diffusion_output'] = diffusion_output
                results['gan_output'] = gan_output
                results['fusion_weights'] = fusion_weights
                
            else:
                results['output'] = diffusion_output
            
            # 3. 时间维度处理（推理时）
            if self.use_temporal_diffusion and temporal_ms is not None:
                B, T, C, H, W = temporal_ms.shape
                temporal_features = temporal_ms.view(B, T, C, H, W)
                temporal_enhanced = self.temporal_diffusion(temporal_features)
                
                # 与空间结果融合
                if 'output' in results:
                    # 简单的加权融合
                    temporal_weight = 0.3
                    results['output'] = (1 - temporal_weight) * results['output'] + temporal_weight * temporal_enhanced
                
                results['temporal_enhanced'] = temporal_enhanced
        
        return results
    
    def compute_loss(self,
                    outputs: Dict[str, torch.Tensor],
                    targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """计算损失"""
        total_losses = {}
        
        # Score diffusion损失已经在forward中计算
        if 'score_loss' in outputs:
            total_losses['score_diffusion'] = outputs['score_loss']
        
        # GAN损失（如果使用混合架构）
        if 'gan_output' in outputs and 'target_ms' in targets:
            gan_loss = F.l1_loss(outputs['gan_output'], targets['target_ms'])
            total_losses['gan_reconstruction'] = gan_loss
        
        # 融合损失
        if 'fused_output' in outputs and 'target_ms' in targets:
            fusion_loss = F.l1_loss(outputs['fused_output'], targets['target_ms'])
            total_losses['fusion'] = fusion_loss
        
        # 时间一致性损失
        if 'temporal_fused' in outputs and 'temporal_target' in targets:
            temporal_loss = F.l1_loss(outputs['temporal_fused'], targets['temporal_target'])
            total_losses['temporal'] = temporal_loss
        
        # 总损失
        total_loss = sum(total_losses.values())
        total_losses['total'] = total_loss
        
        return total_losses

class ScoreDiffusionTrainingWrapper(nn.Module):
    """Score Diffusion训练包装器"""
    
    def __init__(self, fusion_model: EnhancedScoreDiffusionFusion):
        super().__init__()
        self.fusion_model = fusion_model
        
    def training_step(self,
                     batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """训练步骤"""
        rgb_lr = batch['rgb_lr']
        ms_hr = batch['ms_hr']
        temporal_ms = batch.get('temporal_ms', None)
        
        # 前向传播
        outputs = self.fusion_model(rgb_lr, ms_hr, temporal_ms, mode="train")
        
        # 准备目标
        targets = {
            'target_ms': ms_hr,
            'temporal_target': temporal_ms[:, -1] if temporal_ms is not None else None
        }
        
        # 计算损失
        losses = self.fusion_model.compute_loss(outputs, targets)
        
        return losses
    
    def validation_step(self,
                       batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """验证步骤"""
        rgb_lr = batch['rgb_lr']
        ms_hr = batch['ms_hr']
        temporal_ms = batch.get('temporal_ms', None)
        
        with torch.no_grad():
            # 推理
            outputs = self.fusion_model(rgb_lr, None, temporal_ms, mode="inference")
            
            # 计算指标
            if 'output' in outputs:
                psnr = self.compute_psnr(outputs['output'], ms_hr)
                ssim = self.compute_ssim(outputs['output'], ms_hr)
                
                metrics = {
                    'psnr': psnr,
                    'ssim': ssim,
                    'prediction': outputs['output']
                }
            else:
                metrics = {}
                
        return metrics
    
    def compute_psnr(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算PSNR"""
        mse = F.mse_loss(pred, target)
        psnr = 20 * torch.log10(1.0 / torch.sqrt(mse))
        return psnr
    
    def compute_ssim(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """简化的SSIM计算"""
        # 这里使用简化版本，实际使用时建议用专门的SSIM库
        mu1 = F.avg_pool2d(pred, 3, 1, 1)
        mu2 = F.avg_pool2d(target, 3, 1, 1)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.avg_pool2d(pred * pred, 3, 1, 1) - mu1_sq
        sigma2_sq = F.avg_pool2d(target * target, 3, 1, 1) - mu2_sq
        sigma12 = F.avg_pool2d(pred * target, 3, 1, 1) - mu1_mu2
        
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / \
                   ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        return ssim_map.mean()