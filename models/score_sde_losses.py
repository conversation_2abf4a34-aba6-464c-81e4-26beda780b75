import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tu<PERSON>, Optional
from .losses import Grad<PERSON><PERSON><PERSON>, PerceptualLoss

class ScoreDiffusionLoss(nn.Module):
    """Score Diffusion SDE专用损失函数"""
    
    def __init__(self,
                 score_weight: float = 1.0,
                 consistency_weight: float = 0.5,
                 temporal_weight: float = 0.3,
                 perceptual_weight: float = 0.1):
        super().__init__()
        
        self.score_weight = score_weight
        self.consistency_weight = consistency_weight
        self.temporal_weight = temporal_weight
        self.perceptual_weight = perceptual_weight
        
        # 子损失函数
        self.gradient_loss = GradientLoss()
        self.perceptual_loss = PerceptualLoss()
        
    def score_matching_loss(self, predicted_score: torch.Tensor, 
                           target_score: torch.Tensor) -> torch.Tensor:
        """Score matching损失"""
        return F.mse_loss(predicted_score, target_score)
    
    def spectral_consistency_loss(self, rgb_feat: torch.Tensor, 
                                ms_feat: torch.Tensor) -> torch.Tensor:
        """光谱一致性损失"""
        # 确保RGB和多光谱特征在对应波段上保持一致性
        if rgb_feat.shape[1] >= 3 and ms_feat.shape[1] >= 3:
            rgb_bands = rgb_feat[:, :3]  # RGB波段
            ms_rgb_bands = ms_feat[:, :3]  # 多光谱中的RGB对应波段
            return F.l1_loss(rgb_bands, ms_rgb_bands)
        return torch.tensor(0.0, device=rgb_feat.device)
    
    def temporal_consistency_loss(self, 
                                current_feat: torch.Tensor,
                                temporal_features: torch.Tensor) -> torch.Tensor:
        """时间一致性损失"""
        if temporal_features is None:
            return torch.tensor(0.0, device=current_feat.device)
            
        # temporal_features: [B, T, C, H, W]
        B, T, C, H, W = temporal_features.shape
        
        # 计算时间维度的变化率
        temporal_diff = torch.diff(temporal_features, dim=1)  # [B, T-1, C, H, W]
        
        # L2正则化，鼓励时间平滑性
        temporal_smooth_loss = torch.mean(temporal_diff ** 2)
        
        # 当前帧与时间序列的一致性
        mean_temporal = torch.mean(temporal_features, dim=1)  # [B, C, H, W]
        consistency_loss = F.l1_loss(current_feat, mean_temporal)
        
        return temporal_smooth_loss + consistency_loss
    
    def forward(self, 
               predicted_score: torch.Tensor,
               target_score: torch.Tensor,
               rgb_feat: Optional[torch.Tensor] = None,
               ms_feat: Optional[torch.Tensor] = None,
               temporal_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Args:
            predicted_score: 预测的score
            target_score: 目标score
            rgb_feat: RGB特征
            ms_feat: 多光谱特征
            temporal_features: 时间序列特征
        """
        losses = {}
        
        # Score matching损失
        score_loss = self.score_matching_loss(predicted_score, target_score)
        losses['score_loss'] = score_loss
        
        # 光谱一致性损失
        if rgb_feat is not None and ms_feat is not None:
            consistency_loss = self.spectral_consistency_loss(rgb_feat, ms_feat)
            losses['consistency_loss'] = consistency_loss
        else:
            losses['consistency_loss'] = torch.tensor(0.0)
        
        # 时间一致性损失
        if temporal_features is not None:
            temporal_loss = self.temporal_consistency_loss(ms_feat, temporal_features)
            losses['temporal_loss'] = temporal_loss
        else:
            losses['temporal_loss'] = torch.tensor(0.0)
        
        # 感知损失（如果有生成结果）
        if rgb_feat is not None and ms_feat is not None:
            perceptual_loss = self.perceptual_loss(ms_feat[:, :3], rgb_feat[:, :3])
            losses['perceptual_loss'] = perceptual_loss
        else:
            losses['perceptual_loss'] = torch.tensor(0.0)
        
        # 总损失
        total_loss = (self.score_weight * losses['score_loss'] +
                     self.consistency_weight * losses['consistency_loss'] +
                     self.temporal_weight * losses['temporal_loss'] +
                     self.perceptual_weight * losses['perceptual_loss'])
        
        losses['total_loss'] = total_loss
        
        return losses

class SpatiotemporalCoherenceLoss(nn.Module):
    """时空连贯性损失"""
    
    def __init__(self, spatial_weight: float = 1.0, temporal_weight: float = 1.0):
        super().__init__()
        self.spatial_weight = spatial_weight
        self.temporal_weight = temporal_weight
        
    def spatial_coherence_loss(self, features: torch.Tensor) -> torch.Tensor:
        """空间连贯性损失"""
        # 计算空间梯度
        grad_x = torch.diff(features, dim=-1)  # 水平梯度
        grad_y = torch.diff(features, dim=-2)  # 垂直梯度
        
        # 梯度平滑性
        grad_x_smooth = torch.diff(grad_x, dim=-1)
        grad_y_smooth = torch.diff(grad_y, dim=-2)
        
        return torch.mean(grad_x_smooth ** 2) + torch.mean(grad_y_smooth ** 2)
    
    def temporal_coherence_loss(self, temporal_sequence: torch.Tensor) -> torch.Tensor:
        """时间连贯性损失"""
        # temporal_sequence: [B, T, C, H, W]
        if temporal_sequence.shape[1] < 2:
            return torch.tensor(0.0, device=temporal_sequence.device)
            
        # 时间梯度
        temporal_grad = torch.diff(temporal_sequence, dim=1)
        
        # 时间平滑性
        temporal_smooth = torch.diff(temporal_grad, dim=1)
        
        return torch.mean(temporal_smooth ** 2)
    
    def forward(self, 
               features: torch.Tensor,
               temporal_sequence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            features: 当前特征 [B, C, H, W]
            temporal_sequence: 时间序列 [B, T, C, H, W]
        """
        spatial_loss = self.spatial_coherence_loss(features)
        
        if temporal_sequence is not None:
            temporal_loss = self.temporal_coherence_loss(temporal_sequence)
        else:
            temporal_loss = torch.tensor(0.0, device=features.device)
            
        return (self.spatial_weight * spatial_loss + 
                self.temporal_weight * temporal_loss)

class AdaptiveWeightedLoss(nn.Module):
    """自适应权重损失函数"""
    
    def __init__(self, num_losses: int, init_weights: Optional[torch.Tensor] = None):
        super().__init__()
        
        if init_weights is None:
            init_weights = torch.ones(num_losses)
            
        # 可学习的权重参数
        self.log_vars = nn.Parameter(torch.log(init_weights))
        
    def forward(self, losses: torch.Tensor) -> torch.Tensor:
        """
        Args:
            losses: [num_losses] 各个损失项
        Returns:
            加权后的总损失
        """
        # 计算权重
        weights = torch.exp(-self.log_vars)
        
        # 加权损失
        weighted_loss = torch.sum(weights * losses + self.log_vars)
        
        return weighted_loss

class ScoreDiffusionComprehensiveLoss(nn.Module):
    """Score Diffusion综合损失函数"""
    
    def __init__(self,
                 score_weight: float = 1.0,
                 consistency_weight: float = 0.5,
                 temporal_weight: float = 0.3,
                 coherence_weight: float = 0.2,
                 adaptive_weights: bool = True):
        super().__init__()
        
        # 各种损失函数
        self.score_diffusion_loss = ScoreDiffusionLoss(
            score_weight, consistency_weight, temporal_weight
        )
        self.coherence_loss = SpatiotemporalCoherenceLoss()
        
        # 自适应权重
        if adaptive_weights:
            self.adaptive_loss = AdaptiveWeightedLoss(4)  # 4个主要损失项
        else:
            self.adaptive_loss = None
            
        self.coherence_weight = coherence_weight
        
    def forward(self,
               predicted_score: torch.Tensor,
               target_score: torch.Tensor,
               rgb_feat: Optional[torch.Tensor] = None,
               ms_feat: Optional[torch.Tensor] = None,
               temporal_features: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        综合损失计算
        """
        # Score diffusion损失
        diffusion_losses = self.score_diffusion_loss(
            predicted_score, target_score, rgb_feat, ms_feat, temporal_features
        )
        
        # 时空连贯性损失
        if ms_feat is not None:
            coherence_loss = self.coherence_loss(ms_feat, temporal_features)
        else:
            coherence_loss = torch.tensor(0.0, device=predicted_score.device)
            
        diffusion_losses['coherence_loss'] = coherence_loss
        
        # 自适应权重或固定权重
        if self.adaptive_loss is not None:
            loss_values = torch.stack([
                diffusion_losses['score_loss'],
                diffusion_losses['consistency_loss'],
                diffusion_losses['temporal_loss'],
                coherence_loss
            ])
            total_loss = self.adaptive_loss(loss_values)
        else:
            total_loss = (diffusion_losses['score_loss'] +
                         diffusion_losses['consistency_loss'] +
                         diffusion_losses['temporal_loss'] +
                         self.coherence_weight * coherence_loss)
        
        diffusion_losses['total_loss'] = total_loss
        
        return diffusion_losses