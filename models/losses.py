import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import torchvision.models as models
from torchvision.models import VGG19_Weights

class GradientLoss(nn.Module):
    """梯度引导损失函数"""
    
    def __init__(self, alpha: float = 1.0):
        super().__init__()
        self.alpha = alpha
        
        # Sobel算子
        self.register_buffer('sobel_x', torch.tensor([
            [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0))
        
        self.register_buffer('sobel_y', torch.tensor([
            [[-1, -2, -1], [0, 0, 0], [1, 2, 1]]
        ], dtype=torch.float32).unsqueeze(0).unsqueeze(0))
        
    def compute_gradient(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """计算图像梯度"""
        # 如果是多通道，转换为灰度
        if x.size(1) > 1:
            gray = torch.mean(x, dim=1, keepdim=True)
        else:
            gray = x
            
        # 计算x和y方向梯度
        grad_x = F.conv2d(gray, self.sobel_x, padding=1)
        grad_y = F.conv2d(gray, self.sobel_y, padding=1)
        
        return grad_x, grad_y
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        计算梯度损失
        Args:
            pred: 预测图像
            target: 目标图像
        """
        pred_grad_x, pred_grad_y = self.compute_gradient(pred)
        target_grad_x, target_grad_y = self.compute_gradient(target)
        
        # 梯度幅值损失
        pred_grad_mag = torch.sqrt(pred_grad_x ** 2 + pred_grad_y ** 2 + 1e-8)
        target_grad_mag = torch.sqrt(target_grad_x ** 2 + target_grad_y ** 2 + 1e-8)
        
        grad_loss = F.l1_loss(pred_grad_mag, target_grad_mag)
        
        # 梯度方向损失
        direction_loss = F.l1_loss(pred_grad_x, target_grad_x) + F.l1_loss(pred_grad_y, target_grad_y)
        
        return self.alpha * (grad_loss + direction_loss)

class PerceptualLoss(nn.Module):
    """感知损失 - 使用预训练VGG网络"""
    
    def __init__(self, feature_layers: List[int] = [3, 8, 15, 22], 
                 weights: List[float] = [1.0, 1.0, 1.0, 1.0]):
        super().__init__()
        
        # 加载预训练VGG19
        vgg = models.vgg19(weights=VGG19_Weights.IMAGENET1K_V1).features
        self.feature_extractor = nn.ModuleList()
        
        current_layer = 0
        for layer_idx in feature_layers:
            layers = []
            for i in range(current_layer, layer_idx + 1):
                layers.append(vgg[i])
            self.feature_extractor.append(nn.Sequential(*layers))
            current_layer = layer_idx + 1
            
        # 冻结参数
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
            
        self.weights = weights
        self.mse_loss = nn.MSELoss()
        
    def normalize_for_vgg(self, x: torch.Tensor) -> torch.Tensor:
        """为VGG网络标准化输入"""
        # 假设输入已经是[0,1]或[-1,1]范围
        if x.min() < 0:
            x = (x + 1) / 2  # [-1,1] -> [0,1]
            
        # 扩展到3通道（如果需要）
        if x.size(1) == 1:
            x = x.repeat(1, 3, 1, 1)
        elif x.size(1) > 3:
            x = x[:, :3, :, :]  # 只取前3个通道
            
        # VGG预处理
        mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1).to(x.device)
        std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1).to(x.device)
        
        return (x - mean) / std
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算感知损失"""
        pred_norm = self.normalize_for_vgg(pred)
        target_norm = self.normalize_for_vgg(target)
        
        total_loss = 0.0
        pred_features = pred_norm
        target_features = target_norm
        
        for i, (extractor, weight) in enumerate(zip(self.feature_extractor, self.weights)):
            pred_features = extractor(pred_features)
            target_features = extractor(target_features)
            
            loss = self.mse_loss(pred_features, target_features)
            total_loss += weight * loss
            
        return total_loss

class SpectralConsistencyLoss(nn.Module):
    """光谱一致性损失 - 确保RGB和多光谱波段的一致性"""
    
    def __init__(self, rgb_bands: List[int] = [0, 1, 2], alpha: float = 1.0):
        super().__init__()
        self.rgb_bands = rgb_bands  # 多光谱中对应RGB的波段索引
        self.alpha = alpha
        self.mse_loss = nn.MSELoss()
        
    def forward(self, rgb_pred: torch.Tensor, ms_pred: torch.Tensor) -> torch.Tensor:
        """
        计算光谱一致性损失
        Args:
            rgb_pred: 预测的RGB图像 [B, 3, H, W]
            ms_pred: 预测的多光谱图像 [B, C, H, W]
        """
        # 提取多光谱中对应的RGB波段
        ms_rgb = ms_pred[:, self.rgb_bands, :, :]
        
        # 计算一致性损失
        consistency_loss = self.mse_loss(rgb_pred, ms_rgb)
        
        return self.alpha * consistency_loss

class TemporalConsistencyLoss(nn.Module):
    """时间一致性损失"""
    
    def __init__(self, alpha: float = 1.0):
        super().__init__()
        self.alpha = alpha
        self.mse_loss = nn.MSELoss()
        
    def forward(self, temporal_sequence: torch.Tensor) -> torch.Tensor:
        """
        计算时间一致性损失
        Args:
            temporal_sequence: [B, T, C, H, W] 时间序列预测
        """
        if temporal_sequence.size(1) < 2:
            return torch.tensor(0.0, device=temporal_sequence.device)
        
        total_loss = 0.0
        for t in range(1, temporal_sequence.size(1)):
            # 计算相邻时间步的差异
            diff = temporal_sequence[:, t] - temporal_sequence[:, t-1]
            # 鼓励平滑变化
            total_loss += torch.mean(diff ** 2)
            
        return self.alpha * total_loss / (temporal_sequence.size(1) - 1)

class AdversarialLoss(nn.Module):
    """对抗损失"""
    
    def __init__(self, loss_type: str = 'lsgan'):
        super().__init__()
        self.loss_type = loss_type
        
        if loss_type == 'lsgan':
            self.criterion = nn.MSELoss()
        elif loss_type == 'vanilla':
            self.criterion = nn.BCEWithLogitsLoss()
        elif loss_type == 'wgan':
            self.criterion = None
        else:
            raise NotImplementedError(f"Adversarial loss {loss_type} not implemented")
    
    def generator_loss(self, fake_scores: List[torch.Tensor]) -> torch.Tensor:
        """生成器对抗损失"""
        if self.loss_type == 'wgan':
            # WGAN: 最大化判别器输出
            return -torch.mean(torch.cat([score.view(-1) for score in fake_scores]))
        else:
            # LSGAN/vanilla: 让判别器认为生成图像是真实的
            total_loss = 0.0
            for fake_score in fake_scores:
                if self.loss_type == 'lsgan':
                    target = torch.ones_like(fake_score)
                    loss = self.criterion(fake_score, target)
                else:  # vanilla
                    target = torch.ones_like(fake_score)
                    loss = self.criterion(fake_score, target)
                total_loss += loss
            
            return total_loss / len(fake_scores)
    
    def discriminator_loss(self, real_scores: List[torch.Tensor], 
                          fake_scores: List[torch.Tensor]) -> torch.Tensor:
        """判别器对抗损失"""
        if self.loss_type == 'wgan':
            # WGAN: 最大化真实-虚假的差异
            real_loss = -torch.mean(torch.cat([score.view(-1) for score in real_scores]))
            fake_loss = torch.mean(torch.cat([score.view(-1) for score in fake_scores]))
            return real_loss + fake_loss
        else:
            # LSGAN/vanilla
            total_real_loss = 0.0
            total_fake_loss = 0.0
            
            for real_score, fake_score in zip(real_scores, fake_scores):
                if self.loss_type == 'lsgan':
                    real_target = torch.ones_like(real_score)
                    fake_target = torch.zeros_like(fake_score)
                    real_loss = self.criterion(real_score, real_target)
                    fake_loss = self.criterion(fake_score, fake_target)
                else:  # vanilla
                    real_target = torch.ones_like(real_score)
                    fake_target = torch.zeros_like(fake_score)
                    real_loss = self.criterion(real_score, real_target)
                    fake_loss = self.criterion(fake_score, fake_target)
                
                total_real_loss += real_loss
                total_fake_loss += fake_loss
            
            return (total_real_loss + total_fake_loss) / len(real_scores)

class ComprehensiveLoss(nn.Module):
    """综合损失函数"""
    
    def __init__(self, config: Dict):
        super().__init__()
        
        # 各种损失函数
        self.reconstruction_loss = nn.L1Loss()
        self.gradient_loss = GradientLoss(alpha=config.get('gradient_alpha', 1.0))
        self.perceptual_loss = PerceptualLoss()
        self.spectral_loss = SpectralConsistencyLoss(alpha=config.get('spectral_alpha', 1.0))
        self.temporal_loss = TemporalConsistencyLoss(alpha=config.get('temporal_alpha', 1.0))
        self.adversarial_loss = AdversarialLoss(loss_type=config.get('adv_loss_type', 'lsgan'))
        
        # 损失权重
        self.weights = {
            'reconstruction': config.get('reconstruction_weight', 10.0),
            'gradient': config.get('gradient_weight', 1.0),
            'perceptual': config.get('perceptual_weight', 1.0),
            'spectral': config.get('spectral_weight', 5.0),
            'temporal': config.get('temporal_weight', 1.0),
            'adversarial': config.get('adversarial_weight', 1.0)
        }
    
    def generator_loss(self, pred_fused: torch.Tensor, pred_sr: torch.Tensor,
                      target_ms: torch.Tensor, target_rgb: torch.Tensor,
                      fake_scores: List[torch.Tensor],
                      temporal_sequence: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """计算生成器总损失"""
        losses = {}
        
        # 重建损失
        losses['reconstruction'] = self.reconstruction_loss(pred_fused, target_ms)
        
        # 梯度引导损失
        losses['gradient'] = self.gradient_loss(pred_fused, target_ms)
        
        # 感知损失
        losses['perceptual'] = self.perceptual_loss(pred_fused, target_ms)
        
        # 光谱一致性损失
        if pred_sr is not None and target_rgb is not None:
            losses['spectral'] = self.spectral_loss(pred_sr, pred_fused)
        else:
            losses['spectral'] = torch.tensor(0.0, device=pred_fused.device)
        
        # 时间一致性损失
        if temporal_sequence is not None:
            losses['temporal'] = self.temporal_loss(temporal_sequence)
        else:
            losses['temporal'] = torch.tensor(0.0, device=pred_fused.device)
        
        # 对抗损失
        losses['adversarial'] = self.adversarial_loss.generator_loss(fake_scores)
        
        # 计算总损失
        total_loss = sum(self.weights[key] * loss for key, loss in losses.items())
        losses['total'] = total_loss
        
        return losses
    
    def discriminator_loss(self, real_scores: List[torch.Tensor], 
                          fake_scores: List[torch.Tensor]) -> torch.Tensor:
        """计算判别器损失"""
        return self.adversarial_loss.discriminator_loss(real_scores, fake_scores)

class FeatureMatchingLoss(nn.Module):
    """特征匹配损失 - 用于稳定GAN训练"""
    
    def __init__(self, alpha: float = 10.0):
        super().__init__()
        self.alpha = alpha
        self.l1_loss = nn.L1Loss()
    
    def forward(self, real_features: List[torch.Tensor], 
                fake_features: List[torch.Tensor]) -> torch.Tensor:
        """
        计算特征匹配损失
        Args:
            real_features: 判别器从真实图像提取的中间特征
            fake_features: 判别器从生成图像提取的中间特征
        """
        total_loss = 0.0
        
        for real_feat, fake_feat in zip(real_features, fake_features):
            loss = self.l1_loss(fake_feat, real_feat.detach())
            total_loss += loss
        
        return self.alpha * total_loss / len(real_features)

class EdgePreservingLoss(nn.Module):
    """边缘保持损失"""
    
    def __init__(self, alpha: float = 1.0):
        super().__init__()
        self.alpha = alpha
        
        # Laplacian算子
        self.register_buffer('laplacian_kernel', torch.tensor([
            [[0, -1, 0], [-1, 4, -1], [0, -1, 0]]
        ], dtype=torch.float32).unsqueeze(0))
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """计算边缘保持损失"""
        # 转换为灰度
        if pred.size(1) > 1:
            pred_gray = torch.mean(pred, dim=1, keepdim=True)
            target_gray = torch.mean(target, dim=1, keepdim=True)
        else:
            pred_gray = pred
            target_gray = target
        
        # 应用Laplacian算子检测边缘
        pred_edges = F.conv2d(pred_gray, self.laplacian_kernel, padding=1)
        target_edges = F.conv2d(target_gray, self.laplacian_kernel, padding=1)
        
        # 计算边缘损失
        edge_loss = F.l1_loss(pred_edges, target_edges)
        
        return self.alpha * edge_loss