# UAV RGB与Sentinel-2多光谱影像时空融合超分辨率算法

## 项目概述
本项目实现了基于GAN的无人机RGB与Sentinel-2多光谱影像时空融合和超分辨率算法，旨在生成与哨兵影像时空分辨率一致的多光谱高分辨率无人机影像。

## 核心创新
- **梯度引导的时空融合机制**：利用梯度信息指导纹理细节保持
- **RGB波段纹理与多光谱一致性约束**：确保RGB和多光谱波段的光谱一致性
- **注意力机制增强的GAN架构**：集成空间注意力、跨模态注意力、时间注意力等多种注意力机制
- **无监督时空融合+超分辨率**：同时实现时空融合和超分辨率重建

## 算法特点
1. **多注意力机制**：
   - 空间注意力：增强重要空间区域
   - 跨模态注意力：融合RGB和多光谱特征
   - 时间注意力：处理多时相Sentinel-2数据
   - 梯度引导注意力：保持纹理细节

2. **综合损失函数**：
   - 重建损失：保证基本图像质量
   - 梯度损失：保持纹理和边缘信息
   - 感知损失：提高视觉质量
   - 光谱一致性损失：确保多光谱波段一致性
   - 对抗损失：增强真实感

3. **多尺度判别器**：提高生成图像的真实性和细节

## 项目结构
```
UAV/
├── data/                    # 数据目录
│   ├── train/              # 训练数据
│   ├── val/                # 验证数据
│   └── test/               # 测试数据
├── models/                  # 模型定义
│   ├── attention.py        # 注意力机制模块
│   ├── gan_network.py      # GAN网络架构
│   ├── losses.py           # 损失函数
│   └── __init__.py
├── utils/                   # 工具函数
│   ├── data_processor.py   # 数据处理
│   └── __init__.py
├── configs/                 # 配置文件
│   └── config.yaml         # 主配置文件
├── scripts/                 # 脚本文件
│   ├── train.py            # 训练脚本
│   ├── inference.py        # 推理脚本
│   ├── prepare_data.py     # 数据准备脚本
│   ├── train.sh            # 训练Shell脚本
│   └── inference.sh        # 推理Shell脚本
├── results/                 # 结果输出目录
├── requirements.txt         # Python依赖包
└── README.md               # 项目说明
```

## 环境要求
- Python 3.8+
- PyTorch 1.9+
- CUDA 11.0+ (推荐)
- 其他依赖见 `requirements.txt`

## 安装与配置

### 1. 环境安装
```bash
# 克隆项目
git clone <repository-url>
cd UAV

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备
数据应按以下结构组织：
```
data/
├── train/
│   ├── rgb/                # RGB影像
│   ├── sentinel2/          # Sentinel-2多光谱影像
│   └── ground_truth/       # 真值数据(可选)
├── val/
└── test/
```

使用数据准备脚本：
```bash
python scripts/prepare_data.py --data_dir /path/to/your/data --output_dir data
```

### 3. 配置修改
编辑 `configs/config.yaml` 文件，根据你的数据情况修改相关路径和参数。

## 使用方法

### 训练模型
```bash
# 使用配置文件训练
python scripts/train.py --config configs/config.yaml

# 或使用Shell脚本
./scripts/train.sh
```

### 恢复训练
```bash
python scripts/train.py --config configs/config.yaml --resume results/checkpoint_latest.pth
```

### 推理测试

#### 单个图像推理
```bash
python scripts/inference.py \
    --config configs/config.yaml \
    --checkpoint results/checkpoint_best.pth \
    --rgb_path data/test/rgb/sample.tif \
    --s2_paths data/test/sentinel2/sample_20220301.tif data/test/sentinel2/sample_20220315.tif \
    --output_dir inference_results
```

#### 批量推理
```bash
python scripts/inference.py \
    --config configs/config.yaml \
    --checkpoint results/checkpoint_best.pth \
    --batch_mode \
    --rgb_list data/test_rgb_list.txt \
    --s2_list data/test_s2_list.txt \
    --output_dir inference_results
```

#### 模型评估
```bash
python scripts/inference.py \
    --config configs/config.yaml \
    --checkpoint results/checkpoint_best.pth \
    --batch_mode \
    --rgb_list data/test_rgb_list.txt \
    --s2_list data/test_s2_list.txt \
    --output_dir evaluation_results \
    --evaluate \
    --gt_list data/test_gt_list.txt
```

## 模型架构

### 生成器 (FusionGenerator)
- **特征提取器**：分别提取RGB和多光谱特征
- **跨模态注意力**：融合不同模态特征
- **多尺度注意力**：增强多尺度特征表示
- **梯度引导注意力**：保持纹理细节
- **解码器**：渐进式上采样生成高分辨率结果

### 判别器
- **多尺度判别器**：在不同尺度上判别真假
- **时间判别器**（可选）：确保时间一致性

### 损失函数
- **重建损失**：L1损失确保基本重建质量
- **梯度损失**：保持图像梯度信息
- **感知损失**：基于VGG特征的感知质量
- **光谱一致性损失**：确保RGB和多光谱波段一致性
- **对抗损失**：GAN对抗训练

## 评估指标
- **PSNR**：峰值信噪比
- **SSIM**：结构相似性指数
- **RMSE**：均方根误差
- **MAE**：平均绝对误差
- **SAM**：光谱角制图器

## 验证方法
建议使用同时间段的WorldView或其他高分辨率多光谱卫星影像作为验证数据，通过定量和定性评估验证算法效果。

## 注意事项
1. 确保RGB和Sentinel-2影像的地理配准精度
2. 注意不同传感器间的光谱响应差异
3. 根据具体应用场景调整损失函数权重
4. 建议使用多GPU训练以提高效率

## 引用
如果您使用了本项目的代码或方法，请引用相关论文。

## 许可证
本项目遵循MIT许可证。

## 联系方式
如有问题或建议，请提交Issue或联系项目维护者。