# Score Diffusion SDE时空融合配置文件

# 数据配置
data:
  # 图像尺寸
  image_size: [256, 256]
  
  # Sentinel-2选择的波段 (B2,B3,B4,B8 = Blue,Green,Red,NIR)
  sentinel2_bands: [2, 3, 4, 8]
  
  # 数据路径配置
  train_dir: "data/train"
  val_dir: "data/val"
  test_dir: "data/test"
  
  # 时间序列配置
  temporal_window: 8  # 时间窗口大小
  temporal_stride: 1  # 时间步长

# Score Diffusion SDE模型配置
model:
  # 输入输出通道数
  rgb_channels: 3
  ms_channels: 4
  output_channels: 4
  
  # 网络基础通道数
  base_channels: 64
  
  # Score Diffusion参数
  num_timesteps: 1000      # 扩散时间步数
  sigma_min: 0.01          # 最小噪声标准差
  sigma_max: 50.0          # 最大噪声标准差
  
  # 高级功能开关
  use_temporal_diffusion: true    # 启用时间维度扩散
  use_hybrid_architecture: true   # 启用混合架构(GAN+Diffusion)
  
  # 注意力机制配置
  attention_heads: 8
  attention_hidden_dim: 256
  
  # 时间嵌入维度
  time_embed_dim: 128

# 损失函数配置
loss:
  # Score Diffusion损失权重
  score_weight: 1.0
  consistency_weight: 0.5
  temporal_weight: 0.3
  coherence_weight: 0.2
  perceptual_weight: 0.1
  
  # 自适应权重学习
  adaptive_weights: true
  
  # 损失函数参数
  spatial_coherence_weight: 1.0
  temporal_coherence_weight: 1.0

# 训练配置
training:
  # 基本训练参数
  num_epochs: 300
  batch_size: 4          # Score Diffusion内存消耗较大，减小batch size
  val_batch_size: 2
  num_workers: 4
  
  # 多级学习率
  score_lr: 0.0001       # Score网络学习率
  gan_lr: 0.0002         # GAN网络学习率 (如果使用混合架构)
  base_lr: 0.0001        # 其他模块学习率
  min_lr: 0.000001       # 最小学习率
  
  # 优化器参数
  beta1: 0.5
  beta2: 0.999
  weight_decay: 0.0001
  
  # 梯度裁剪
  grad_clip: 1.0
  
  # 学习率调度
  scheduler_type: "cosine"  # cosine, step, plateau
  
  # 保存和日志
  output_dir: "results"
  save_interval: 20
  log_interval: 50
  
  # 其他
  seed: 42

# 推理配置
inference:
  # 采样参数
  sampling_method: "predictor_corrector"  # euler_maruyama, predictor_corrector
  sampling_steps: 500                     # 采样步数
  corrector_steps: 1                      # 修正步数
  snr: 0.16                              # 信噪比
  
  # 输出格式
  save_visualization: true
  save_multispectral: true
  save_rgb: true
  save_intermediate: true     # 保存中间结果
  
  # 数据范围
  output_range: [0, 1]

# 评估配置
evaluation:
  metrics:
    - "psnr"
    - "ssim" 
    - "rmse"
    - "mae"
    - "sam"      # 光谱角制图器
    - "ergas"    # 相对无量纲全局误差
  
  # 评估时的数据范围
  data_range: 1.0

# 高级配置
advanced:
  # 混合精度训练
  mixed_precision: true
  
  # 梯度累积
  gradient_accumulation_steps: 1
  
  # EMA (指数移动平均)
  use_ema: true
  ema_decay: 0.9999
  
  # 编译优化 (PyTorch 2.0+)
  compile_model: false