# 数据配置
data:
  # 图像尺寸
  image_size: [256, 256]
  
  # Sentinel-2选择的波段 (B2,B3,B4,B8 = Blue,Green,Red,NIR)
  sentinel2_bands: [2, 3, 4, 8]
  
  # 数据路径配置
  train_dir: "data/train"
  val_dir: "data/val"
  test_dir: "data/test"
  
  # 数据集配置文件路径
  config_path: "configs/config.yaml"

# 模型配置
model:
  # 输入输出通道数
  rgb_channels: 3
  ms_channels: 4
  output_channels: 4
  
  # 网络基础通道数
  base_channels: 64
  
  # 判别器配置
  discriminator_scales: 3
  use_temporal_discriminator: false

# 损失函数配置
loss:
  # 损失权重
  reconstruction_weight: 10.0
  gradient_weight: 1.0
  perceptual_weight: 1.0
  spectral_weight: 5.0
  temporal_weight: 1.0
  adversarial_weight: 1.0
  
  # 损失函数参数
  gradient_alpha: 1.0
  spectral_alpha: 1.0
  temporal_alpha: 1.0
  adv_loss_type: "lsgan"  # lsgan, vanilla, wgan

# 训练配置
training:
  # 基本训练参数
  num_epochs: 200
  batch_size: 8
  val_batch_size: 4
  num_workers: 4
  
  # 学习率
  g_lr: 0.0002
  d_lr: 0.0002
  beta1: 0.5
  beta2: 0.999
  
  # 学习率衰减
  lr_decay_epochs: 50
  lr_decay_gamma: 0.5
  
  # 梯度裁剪
  grad_clip: 1.0
  
  # 保存和日志
  output_dir: "results"
  save_interval: 10
  log_interval: 100
  
  # 其他
  seed: 42

# 推理配置
inference:
  # 输出格式
  save_visualization: true
  save_multispectral: true
  save_rgb: true
  
  # 数据范围
  output_range: [0, 1]  # 或者 [-1, 1]

# 评估配置
evaluation:
  metrics:
    - "psnr"
    - "ssim" 
    - "rmse"
    - "mae"
    - "sam"
  
  # 评估时的数据范围
  data_range: 1.0