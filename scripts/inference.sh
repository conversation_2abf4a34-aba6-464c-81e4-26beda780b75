#!/bin/bash

# UAV-Sentinel2 时空融合推理脚本

echo "开始 UAV-Sentinel2 时空融合推理..."

# 配置参数
CONFIG_FILE="configs/config.yaml"
CHECKPOINT="results/checkpoint_best.pth"
OUTPUT_DIR="inference_results/$(date +%Y%m%d_%H%M%S)"

# 检查必要文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

if [ ! -f "$CHECKPOINT" ]; then
    echo "错误: 模型检查点 $CHECKPOINT 不存在"
    exit 1
fi

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 单个图像推理示例
echo "单个图像推理示例:"
python scripts/inference.py \
    --config $CONFIG_FILE \
    --checkpoint $CHECKPOINT \
    --rgb_path data/test/rgb/sample.tif \
    --s2_paths data/test/sentinel2/sample_20220301.tif data/test/sentinel2/sample_20220315.tif \
    --output_dir $OUTPUT_DIR/single

# 批量推理示例（如果有相应的文件列表）
if [ -f "data/test_rgb_list.txt" ] && [ -f "data/test_s2_list.txt" ]; then
    echo "批量推理:"
    python scripts/inference.py \
        --config $CONFIG_FILE \
        --checkpoint $CHECKPOINT \
        --batch_mode \
        --rgb_list data/test_rgb_list.txt \
        --s2_list data/test_s2_list.txt \
        --output_dir $OUTPUT_DIR/batch
    
    # 如果有真值数据，进行评估
    if [ -f "data/test_gt_list.txt" ]; then
        echo "模型评估:"
        python scripts/inference.py \
            --config $CONFIG_FILE \
            --checkpoint $CHECKPOINT \
            --batch_mode \
            --rgb_list data/test_rgb_list.txt \
            --s2_list data/test_s2_list.txt \
            --output_dir $OUTPUT_DIR/evaluation \
            --evaluate \
            --gt_list data/test_gt_list.txt
    fi
fi

echo "推理完成，结果保存在: $OUTPUT_DIR"