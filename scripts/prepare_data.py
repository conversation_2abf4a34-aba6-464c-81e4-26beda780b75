#!/usr/bin/env python3
"""
数据准备脚本 - 用于创建训练所需的数据文件列表
"""

import os
import argparse
from pathlib import Path
from typing import List, Tuple
import random

def create_file_lists(data_dir: str, output_dir: str, train_ratio: float = 0.8):
    """创建训练/验证/测试文件列表"""
    
    data_path = Path(data_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 假设数据结构为：
    # data_dir/
    #   ├── rgb/
    #   ├── sentinel2/
    #   └── ground_truth/ (可选)
    
    rgb_dir = data_path / 'rgb'
    s2_dir = data_path / 'sentinel2' 
    gt_dir = data_path / 'ground_truth'
    
    if not rgb_dir.exists():
        print(f"错误: RGB目录 {rgb_dir} 不存在")
        return
    
    if not s2_dir.exists():
        print(f"错误: Sentinel-2目录 {s2_dir} 不存在")
        return
    
    # 获取RGB文件列表
    rgb_files = list(rgb_dir.glob('*.tif'))
    rgb_files.extend(list(rgb_dir.glob('*.tiff')))
    rgb_files = sorted(rgb_files)
    
    print(f"找到 {len(rgb_files)} 个RGB文件")
    
    # 为每个RGB文件查找对应的Sentinel-2文件
    valid_pairs = []
    
    for rgb_file in rgb_files:
        base_name = rgb_file.stem
        
        # 查找对应的Sentinel-2文件（可能有多个时相）
        s2_pattern = f"{base_name}_*.tif*"
        s2_files = list(s2_dir.glob(s2_pattern))
        s2_files = sorted(s2_files)
        
        if len(s2_files) > 0:
            # 检查是否有对应的真值文件
            gt_file = gt_dir / f"{base_name}.tif" if gt_dir.exists() else None
            if gt_file and not gt_file.exists():
                gt_file = gt_dir / f"{base_name}.tiff"
                if not gt_file.exists():
                    gt_file = None
            
            valid_pairs.append({
                'rgb': str(rgb_file),
                's2_files': [str(f) for f in s2_files],
                'gt': str(gt_file) if gt_file else None
            })
    
    print(f"找到 {len(valid_pairs)} 个有效的数据对")
    
    if len(valid_pairs) == 0:
        print("没有找到有效的数据对，请检查数据目录结构")
        return
    
    # 随机打乱数据
    random.shuffle(valid_pairs)
    
    # 划分训练/验证/测试集
    n_total = len(valid_pairs)
    n_train = int(n_total * train_ratio)
    n_val = int(n_total * 0.1)  # 10% 用于验证
    n_test = n_total - n_train - n_val
    
    train_pairs = valid_pairs[:n_train]
    val_pairs = valid_pairs[n_train:n_train + n_val]
    test_pairs = valid_pairs[n_train + n_val:]
    
    print(f"数据划分: 训练={len(train_pairs)}, 验证={len(val_pairs)}, 测试={len(test_pairs)}")
    
    # 创建文件列表
    for split, pairs in [('train', train_pairs), ('val', val_pairs), ('test', test_pairs)]:
        if len(pairs) == 0:
            continue
            
        # RGB列表
        rgb_list_file = output_path / f"{split}_rgb_list.txt"
        with open(rgb_list_file, 'w') as f:
            for pair in pairs:
                f.write(f"{pair['rgb']}\n")
        
        # Sentinel-2列表
        s2_list_file = output_path / f"{split}_s2_list.txt"
        with open(s2_list_file, 'w') as f:
            for pair in pairs:
                s2_line = ','.join(pair['s2_files'])
                f.write(f"{s2_line}\n")
        
        # 真值列表（如果存在）
        if any(pair['gt'] for pair in pairs):
            gt_list_file = output_path / f"{split}_gt_list.txt"
            with open(gt_list_file, 'w') as f:
                for pair in pairs:
                    if pair['gt']:
                        f.write(f"{pair['gt']}\n")
                    else:
                        f.write("None\n")
    
    print(f"文件列表已保存到 {output_path}")

def main():
    parser = argparse.ArgumentParser(description='准备UAV-Sentinel2融合训练数据')
    parser.add_argument('--data_dir', type=str, required=True, 
                      help='数据根目录，应包含rgb和sentinel2子目录')
    parser.add_argument('--output_dir', type=str, default='data',
                      help='输出文件列表的目录')
    parser.add_argument('--train_ratio', type=float, default=0.8,
                      help='训练集比例 (默认: 0.8)')
    parser.add_argument('--seed', type=int, default=42,
                      help='随机种子')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 创建文件列表
    create_file_lists(args.data_dir, args.output_dir, args.train_ratio)

if __name__ == '__main__':
    main()