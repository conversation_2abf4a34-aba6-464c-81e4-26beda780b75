import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import yaml
import argparse
import os
import time
from pathlib import Path
import numpy as np
from tqdm import tqdm
from typing import Dict, Tuple

import sys
sys.path.append('..')

from models.gan_network import FusionGenerator, MultiScaleDiscriminator, TemporalDiscriminator
from models.losses import ComprehensiveLoss
from utils.data_processor import FusionDataset

class Trainer:
    """训练器类"""
    
    def __init__(self, config_path: str):
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # 创建输出目录
        self.output_dir = Path(self.config['training']['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化网络
        self._init_networks()
        
        # 初始化损失函数
        self._init_losses()
        
        # 初始化优化器
        self._init_optimizers()
        
        # 初始化数据加载器
        self._init_dataloaders()
        
        # 初始化记录器
        self._init_logging()
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        
    def _init_networks(self):
        """初始化网络"""
        # 生成器
        self.generator = FusionGenerator(
            rgb_channels=self.config['model']['rgb_channels'],
            ms_channels=self.config['model']['ms_channels'],
            output_channels=self.config['model']['output_channels'],
            base_channels=self.config['model']['base_channels']
        ).to(self.device)
        
        # 空间判别器
        self.spatial_discriminator = MultiScaleDiscriminator(
            in_channels=self.config['model']['output_channels'],
            num_scales=self.config['model']['discriminator_scales']
        ).to(self.device)
        
        # 时间判别器（可选）
        if self.config['model'].get('use_temporal_discriminator', False):
            self.temporal_discriminator = TemporalDiscriminator(
                in_channels=self.config['model']['output_channels']
            ).to(self.device)
        else:
            self.temporal_discriminator = None
        
        print(f"Generator parameters: {sum(p.numel() for p in self.generator.parameters()):,}")
        print(f"Spatial Discriminator parameters: {sum(p.numel() for p in self.spatial_discriminator.parameters()):,}")
        
    def _init_losses(self):
        """初始化损失函数"""
        self.criterion = ComprehensiveLoss(self.config['loss']).to(self.device)
        
    def _init_optimizers(self):
        """初始化优化器"""
        # 生成器优化器
        self.optimizer_G = optim.Adam(
            self.generator.parameters(),
            lr=self.config['training']['g_lr'],
            betas=(self.config['training']['beta1'], self.config['training']['beta2'])
        )
        
        # 判别器优化器
        discriminator_params = list(self.spatial_discriminator.parameters())
        if self.temporal_discriminator is not None:
            discriminator_params.extend(list(self.temporal_discriminator.parameters()))
            
        self.optimizer_D = optim.Adam(
            discriminator_params,
            lr=self.config['training']['d_lr'],
            betas=(self.config['training']['beta1'], self.config['training']['beta2'])
        )
        
        # 学习率调度器
        self.scheduler_G = optim.lr_scheduler.StepLR(
            self.optimizer_G,
            step_size=self.config['training']['lr_decay_epochs'],
            gamma=self.config['training']['lr_decay_gamma']
        )
        
        self.scheduler_D = optim.lr_scheduler.StepLR(
            self.optimizer_D,
            step_size=self.config['training']['lr_decay_epochs'],
            gamma=self.config['training']['lr_decay_gamma']
        )
        
    def _init_dataloaders(self):
        """初始化数据加载器"""
        # 训练数据集
        train_dataset = FusionDataset(
            config_path=self.config['data']['config_path'],
            mode='train'
        )
        
        self.train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            num_workers=self.config['training']['num_workers'],
            pin_memory=True
        )
        
        # 验证数据集
        try:
            val_dataset = FusionDataset(
                config_path=self.config['data']['config_path'],
                mode='val'
            )
            
            self.val_loader = DataLoader(
                val_dataset,
                batch_size=self.config['training']['val_batch_size'],
                shuffle=False,
                num_workers=self.config['training']['num_workers'],
                pin_memory=True
            )
        except:
            print("Warning: Validation dataset not found, using training data for validation")
            self.val_loader = None
        
        print(f"Training samples: {len(train_dataset)}")
        if self.val_loader:
            print(f"Validation samples: {len(val_dataset)}")
    
    def _init_logging(self):
        """初始化日志记录"""
        self.writer = SummaryWriter(self.output_dir / 'logs')
        
    def train_one_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.generator.train()
        self.spatial_discriminator.train()
        if self.temporal_discriminator:
            self.temporal_discriminator.train()
        
        epoch_losses = {
            'G_total': 0.0, 'G_reconstruction': 0.0, 'G_gradient': 0.0,
            'G_perceptual': 0.0, 'G_spectral': 0.0, 'G_adversarial': 0.0,
            'D_total': 0.0, 'D_real': 0.0, 'D_fake': 0.0
        }
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch_idx, batch in enumerate(pbar):
            # 数据准备
            rgb_lr = batch['rgb_lr'].to(self.device)
            rgb_hr = batch['rgb_hr'].to(self.device)
            s2_ms = batch['s2_ms'].to(self.device)
            
            batch_size = rgb_lr.size(0)
            
            # ===================
            # 训练生成器
            # ===================
            self.optimizer_G.zero_grad()
            
            # 生成融合图像
            fused_ms, sr_rgb = self.generator(rgb_lr, s2_ms, rgb_hr)
            
            # 获取判别器分数
            fake_spatial_scores = self.spatial_discriminator(fused_ms)
            
            # 计算生成器损失
            g_losses = self.criterion.generator_loss(
                pred_fused=fused_ms,
                pred_sr=sr_rgb,
                target_ms=s2_ms,
                target_rgb=rgb_hr,
                fake_scores=fake_spatial_scores
            )
            
            # 反向传播
            g_losses['total'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.generator.parameters(), 
                self.config['training']['grad_clip']
            )
            
            self.optimizer_G.step()
            
            # ===================
            # 训练判别器
            # ===================
            self.optimizer_D.zero_grad()
            
            # 真实样本判别
            real_spatial_scores = self.spatial_discriminator(s2_ms)
            
            # 虚假样本判别（detach防止梯度传播到生成器）
            fake_spatial_scores_d = self.spatial_discriminator(fused_ms.detach())
            
            # 计算判别器损失
            d_loss = self.criterion.discriminator_loss(
                real_scores=real_spatial_scores,
                fake_scores=fake_spatial_scores_d
            )
            
            # 反向传播
            d_loss.backward()
            self.optimizer_D.step()
            
            # 更新损失统计
            for key in g_losses:
                if key in epoch_losses:
                    epoch_losses[f'G_{key}'] += g_losses[key].item()
            
            epoch_losses['D_total'] += d_loss.item()
            
            # 更新进度条
            pbar.set_postfix({
                'G_loss': f"{g_losses['total'].item():.4f}",
                'D_loss': f"{d_loss.item():.4f}"
            })
            
            # 记录到tensorboard
            if batch_idx % self.config['training']['log_interval'] == 0:
                for key, value in g_losses.items():
                    self.writer.add_scalar(f'Train/G_{key}', value.item(), self.global_step)
                
                self.writer.add_scalar('Train/D_total', d_loss.item(), self.global_step)
                
                # 记录学习率
                self.writer.add_scalar('Train/lr_G', self.optimizer_G.param_groups[0]['lr'], self.global_step)
                self.writer.add_scalar('Train/lr_D', self.optimizer_D.param_groups[0]['lr'], self.global_step)
            
            self.global_step += 1
        
        # 计算平均损失
        for key in epoch_losses:
            epoch_losses[key] /= len(self.train_loader)
        
        return epoch_losses
    
    def validate(self) -> Dict[str, float]:
        """验证"""
        if self.val_loader is None:
            return {}
        
        self.generator.eval()
        self.spatial_discriminator.eval()
        if self.temporal_discriminator:
            self.temporal_discriminator.eval()
        
        val_losses = {
            'G_total': 0.0, 'G_reconstruction': 0.0, 'G_gradient': 0.0,
            'G_perceptual': 0.0, 'G_spectral': 0.0, 'G_adversarial': 0.0,
            'D_total': 0.0
        }
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                rgb_lr = batch['rgb_lr'].to(self.device)
                rgb_hr = batch['rgb_hr'].to(self.device)
                s2_ms = batch['s2_ms'].to(self.device)
                
                # 生成融合图像
                fused_ms, sr_rgb = self.generator(rgb_lr, s2_ms, rgb_hr)
                
                # 获取判别器分数
                fake_spatial_scores = self.spatial_discriminator(fused_ms)
                real_spatial_scores = self.spatial_discriminator(s2_ms)
                
                # 计算损失
                g_losses = self.criterion.generator_loss(
                    pred_fused=fused_ms,
                    pred_sr=sr_rgb,
                    target_ms=s2_ms,
                    target_rgb=rgb_hr,
                    fake_scores=fake_spatial_scores
                )
                
                d_loss = self.criterion.discriminator_loss(
                    real_scores=real_spatial_scores,
                    fake_scores=fake_spatial_scores
                )
                
                # 累加损失
                for key in g_losses:
                    if f'G_{key}' in val_losses:
                        val_losses[f'G_{key}'] += g_losses[key].item()
                
                val_losses['D_total'] += d_loss.item()
        
        # 计算平均损失
        for key in val_losses:
            val_losses[key] /= len(self.val_loader)
        
        return val_losses
    
    def save_checkpoint(self, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'generator_state_dict': self.generator.state_dict(),
            'spatial_discriminator_state_dict': self.spatial_discriminator.state_dict(),
            'optimizer_G_state_dict': self.optimizer_G.state_dict(),
            'optimizer_D_state_dict': self.optimizer_D.state_dict(),
            'scheduler_G_state_dict': self.scheduler_G.state_dict(),
            'scheduler_D_state_dict': self.scheduler_D.state_dict(),
            'config': self.config
        }
        
        if self.temporal_discriminator:
            checkpoint['temporal_discriminator_state_dict'] = self.temporal_discriminator.state_dict()
        
        # 保存最新检查点
        torch.save(checkpoint, self.output_dir / 'checkpoint_latest.pth')
        
        # 保存最佳检查点
        if is_best:
            torch.save(checkpoint, self.output_dir / 'checkpoint_best.pth')
        
        # 保存周期性检查点
        if self.current_epoch % self.config['training']['save_interval'] == 0:
            torch.save(checkpoint, self.output_dir / f'checkpoint_epoch_{self.current_epoch}.pth')
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.spatial_discriminator.load_state_dict(checkpoint['spatial_discriminator_state_dict'])
        
        if 'temporal_discriminator_state_dict' in checkpoint and self.temporal_discriminator:
            self.temporal_discriminator.load_state_dict(checkpoint['temporal_discriminator_state_dict'])
        
        self.optimizer_G.load_state_dict(checkpoint['optimizer_G_state_dict'])
        self.optimizer_D.load_state_dict(checkpoint['optimizer_D_state_dict'])
        self.scheduler_G.load_state_dict(checkpoint['scheduler_G_state_dict'])
        self.scheduler_D.load_state_dict(checkpoint['scheduler_D_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        
        print(f"Loaded checkpoint from epoch {self.current_epoch}")
    
    def train(self):
        """主训练循环"""
        print("Starting training...")
        best_val_loss = float('inf')
        
        for epoch in range(self.current_epoch, self.config['training']['num_epochs']):
            self.current_epoch = epoch
            start_time = time.time()
            
            # 训练一个epoch
            train_losses = self.train_one_epoch()
            
            # 验证
            val_losses = self.validate()
            
            # 更新学习率
            self.scheduler_G.step()
            self.scheduler_D.step()
            
            # 记录损失到tensorboard
            for key, value in train_losses.items():
                self.writer.add_scalar(f'Epoch/Train_{key}', value, epoch)
            
            for key, value in val_losses.items():
                self.writer.add_scalar(f'Epoch/Val_{key}', value, epoch)
            
            # 打印训练信息
            epoch_time = time.time() - start_time
            print(f"\nEpoch {epoch}/{self.config['training']['num_epochs']}")
            print(f"Time: {epoch_time:.2f}s")
            print(f"Train - G_total: {train_losses['G_total']:.4f}, D_total: {train_losses['D_total']:.4f}")
            
            if val_losses:
                print(f"Val - G_total: {val_losses['G_total']:.4f}, D_total: {val_losses['D_total']:.4f}")
                current_val_loss = val_losses['G_total']
            else:
                current_val_loss = train_losses['G_total']
            
            # 保存检查点
            is_best = current_val_loss < best_val_loss
            if is_best:
                best_val_loss = current_val_loss
                print(f"New best model with validation loss: {best_val_loss:.4f}")
            
            self.save_checkpoint(is_best=is_best)
        
        print("Training completed!")
        self.writer.close()

def main():
    parser = argparse.ArgumentParser(description='Train UAV-Sentinel2 Fusion Model')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--resume', type=str, help='Path to checkpoint to resume from')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = Trainer(args.config)
    
    # 恢复训练（如果指定）
    if args.resume:
        trainer.load_checkpoint(args.resume)
    
    # 开始训练
    trainer.train()

if __name__ == '__main__':
    main()