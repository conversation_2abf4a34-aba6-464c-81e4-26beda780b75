import torch
import torch.nn.functional as F
import numpy as np
import cv2
import rasterio
from rasterio.transform import from_bounds
import argparse
import yaml
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
from skimage.metrics import peak_signal_noise_ratio, structural_similarity
import time

import sys
sys.path.append('..')

from models.gan_network import FusionGenerator
from utils.data_processor import RGBProcessor, Sentinel2Processor

class FusionInference:
    """时空融合推理类"""
    
    def __init__(self, config_path: str, checkpoint_path: str):
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # 初始化处理器
        self.rgb_processor = RGBProcessor(
            target_size=tuple(self.config['data']['image_size'])
        )
        self.s2_processor = Sentinel2Processor(
            target_size=tuple(self.config['data']['image_size']),
            selected_bands=self.config['data']['sentinel2_bands']
        )
        
        # 加载模型
        self._load_model(checkpoint_path)
        
    def _load_model(self, checkpoint_path: str):
        """加载训练好的模型"""
        # 初始化生成器
        self.generator = FusionGenerator(
            rgb_channels=self.config['model']['rgb_channels'],
            ms_channels=self.config['model']['ms_channels'],
            output_channels=self.config['model']['output_channels'],
            base_channels=self.config['model']['base_channels']
        ).to(self.device)
        
        # 加载权重
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.generator.eval()
        
        print(f"Model loaded from {checkpoint_path}")
        print(f"Trained for {checkpoint['epoch']} epochs")
    
    def preprocess_inputs(self, rgb_path: str, s2_paths: List[str], 
                         target_time: Optional[float] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """预处理输入数据"""
        # 加载RGB影像
        rgb_image = self.rgb_processor.load_rgb_image(rgb_path)
        rgb_tensor = self.rgb_processor.preprocess_rgb(rgb_image)
        
        # 创建低分辨率版本
        rgb_lr = F.interpolate(
            rgb_tensor.unsqueeze(0), 
            scale_factor=0.25, 
            mode='bilinear', 
            align_corners=False
        ).squeeze(0)
        
        # 加载和处理Sentinel-2影像
        s2_images = []
        times = []
        
        for i, s2_path in enumerate(s2_paths):
            s2_image = self.s2_processor.load_sentinel2_image(s2_path)
            s2_images.append(s2_image)
            # 简化时间处理
            times.append(float(i))
        
        # 时间插值（如果有多个时相）
        if len(s2_images) > 1 and target_time is not None:
            s2_interpolated = self.s2_processor.temporal_interpolation(
                s2_images, target_time, times
            )
        else:
            s2_interpolated = s2_images[0]
        
        s2_tensor = self.s2_processor.preprocess_sentinel2(s2_interpolated)
        
        return rgb_lr, s2_tensor, rgb_tensor
    
    def inference_single(self, rgb_path: str, s2_paths: List[str], 
                        target_time: Optional[float] = None) -> Tuple[np.ndarray, np.ndarray]:
        """单个样本推理"""
        # 预处理
        rgb_lr, s2_ms, rgb_hr = self.preprocess_inputs(rgb_path, s2_paths, target_time)
        
        # 添加batch维度
        rgb_lr = rgb_lr.unsqueeze(0).to(self.device)
        s2_ms = s2_ms.unsqueeze(0).to(self.device)
        rgb_hr = rgb_hr.unsqueeze(0).to(self.device)
        
        # 推理
        with torch.no_grad():
            start_time = time.time()
            fused_ms, sr_rgb = self.generator(rgb_lr, s2_ms, rgb_hr)
            inference_time = time.time() - start_time
        
        # 转换为numpy
        fused_ms = fused_ms.squeeze(0).cpu().numpy()
        sr_rgb = sr_rgb.squeeze(0).cpu().numpy() if sr_rgb is not None else None
        
        print(f"Inference time: {inference_time:.4f}s")
        
        return fused_ms, sr_rgb
    
    def batch_inference(self, rgb_paths: List[str], s2_paths_list: List[List[str]], 
                       output_dir: str, target_times: Optional[List[float]] = None):
        """批量推理"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if target_times is None:
            target_times = [None] * len(rgb_paths)
        
        total_time = 0
        results = []
        
        for i, (rgb_path, s2_paths, target_time) in enumerate(
            tqdm(zip(rgb_paths, s2_paths_list, target_times), total=len(rgb_paths))
        ):
            try:
                # 推理
                start_time = time.time()
                fused_ms, sr_rgb = self.inference_single(rgb_path, s2_paths, target_time)
                inference_time = time.time() - start_time
                total_time += inference_time
                
                # 保存结果
                base_name = Path(rgb_path).stem
                
                # 保存融合的多光谱影像
                self.save_multispectral(
                    fused_ms, 
                    output_dir / f"{base_name}_fused_ms.tif"
                )
                
                # 保存超分辨率RGB影像（如果存在）
                if sr_rgb is not None:
                    self.save_rgb(
                        sr_rgb, 
                        output_dir / f"{base_name}_sr_rgb.tif"
                    )
                
                # 保存可视化结果
                self.save_visualization(
                    fused_ms, sr_rgb, 
                    output_dir / f"{base_name}_visualization.png"
                )
                
                results.append({
                    'file': base_name,
                    'inference_time': inference_time,
                    'success': True
                })
                
            except Exception as e:
                print(f"Error processing {rgb_path}: {e}")
                results.append({
                    'file': Path(rgb_path).stem,
                    'inference_time': 0,
                    'success': False,
                    'error': str(e)
                })
        
        # 打印统计信息
        successful = sum(1 for r in results if r['success'])
        avg_time = total_time / successful if successful > 0 else 0
        
        print(f"\nBatch inference completed:")
        print(f"Successful: {successful}/{len(rgb_paths)}")
        print(f"Average inference time: {avg_time:.4f}s")
        print(f"Total time: {total_time:.2f}s")
        
        return results
    
    def save_multispectral(self, ms_array: np.ndarray, output_path: Path):
        """保存多光谱影像"""
        # 确保数据范围正确
        if ms_array.min() >= -1 and ms_array.max() <= 1:
            # 从[-1,1]转换到[0,1]
            ms_array = (ms_array + 1) / 2
        
        # 限制到[0,1]范围
        ms_array = np.clip(ms_array, 0, 1)
        
        # 转换为uint16格式保存
        ms_uint16 = (ms_array * 65535).astype(np.uint16)
        
        # 写入文件
        height, width = ms_array.shape[1:]
        with rasterio.open(
            output_path,
            'w',
            driver='GTiff',
            height=height,
            width=width,
            count=ms_array.shape[0],
            dtype=rasterio.uint16,
            compress='lzw'
        ) as dst:
            dst.write(ms_uint16)
    
    def save_rgb(self, rgb_array: np.ndarray, output_path: Path):
        """保存RGB影像"""
        # 确保数据范围正确
        if rgb_array.min() >= -1 and rgb_array.max() <= 1:
            # 从[-1,1]转换到[0,1]
            rgb_array = (rgb_array + 1) / 2
        
        # 转换为HWC格式
        rgb_hwc = np.transpose(rgb_array, (1, 2, 0))
        rgb_hwc = np.clip(rgb_hwc, 0, 1)
        
        # 转换为uint8并保存
        rgb_uint8 = (rgb_hwc * 255).astype(np.uint8)
        cv2.imwrite(str(output_path), cv2.cvtColor(rgb_uint8, cv2.COLOR_RGB2BGR))
    
    def save_visualization(self, fused_ms: np.ndarray, sr_rgb: Optional[np.ndarray], 
                          output_path: Path):
        """保存可视化结果"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 融合多光谱的RGB可视化（假设前3个波段对应RGB）
        ms_rgb = fused_ms[:3]  # 取前3个波段
        ms_rgb = np.transpose(ms_rgb, (1, 2, 0))
        ms_rgb = np.clip((ms_rgb + 1) / 2, 0, 1)  # [-1,1] -> [0,1]
        
        axes[0].imshow(ms_rgb)
        axes[0].set_title('Fused Multispectral (RGB)')
        axes[0].axis('off')
        
        # 近红外波段可视化（如果存在）
        if fused_ms.shape[0] > 3:
            nir_band = fused_ms[3]  # 第4个波段通常是近红外
            nir_normalized = (nir_band + 1) / 2
            axes[1].imshow(nir_normalized, cmap='RdYlBu_r')
            axes[1].set_title('Near-Infrared Band')
            axes[1].axis('off')
        else:
            axes[1].text(0.5, 0.5, 'NIR Not Available', ha='center', va='center')
            axes[1].axis('off')
        
        # 超分辨率RGB（如果存在）
        if sr_rgb is not None:
            sr_rgb_vis = np.transpose(sr_rgb, (1, 2, 0))
            sr_rgb_vis = np.clip((sr_rgb_vis + 1) / 2, 0, 1)
            axes[2].imshow(sr_rgb_vis)
            axes[2].set_title('Super-Resolution RGB')
        else:
            axes[2].text(0.5, 0.5, 'SR RGB Not Available', ha='center', va='center')
        
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, config_path: str):
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
    
    def calculate_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        metrics = {}
        
        # 确保数据范围一致
        pred = np.clip(pred, 0, 1)
        target = np.clip(target, 0, 1)
        
        # PSNR
        metrics['psnr'] = peak_signal_noise_ratio(target, pred, data_range=1.0)
        
        # SSIM（对每个波段分别计算，然后取平均）
        ssim_values = []
        for i in range(pred.shape[0]):
            ssim = structural_similarity(
                target[i], pred[i], 
                data_range=1.0,
                gaussian_weights=True,
                use_sample_covariance=False
            )
            ssim_values.append(ssim)
        metrics['ssim'] = np.mean(ssim_values)
        
        # RMSE
        mse = np.mean((pred - target) ** 2)
        metrics['rmse'] = np.sqrt(mse)
        
        # MAE
        metrics['mae'] = np.mean(np.abs(pred - target))
        
        # SAM (Spectral Angle Mapper)
        sam_values = []
        h, w = pred.shape[1:]
        for i in range(h):
            for j in range(w):
                pred_spectrum = pred[:, i, j]
                target_spectrum = target[:, i, j]
                
                # 计算光谱角
                dot_product = np.dot(pred_spectrum, target_spectrum)
                norm_pred = np.linalg.norm(pred_spectrum)
                norm_target = np.linalg.norm(target_spectrum)
                
                if norm_pred > 0 and norm_target > 0:
                    cos_angle = dot_product / (norm_pred * norm_target)
                    cos_angle = np.clip(cos_angle, -1, 1)
                    angle = np.arccos(cos_angle)
                    sam_values.append(angle)
        
        metrics['sam'] = np.mean(sam_values) if sam_values else 0.0
        
        return metrics
    
    def evaluate_model(self, inference_engine: FusionInference, 
                      rgb_paths: List[str], s2_paths_list: List[List[str]], 
                      gt_paths: List[str]) -> Dict[str, float]:
        """评估模型性能"""
        all_metrics = []
        
        print("Evaluating model performance...")
        
        for rgb_path, s2_paths, gt_path in tqdm(
            zip(rgb_paths, s2_paths_list, gt_paths), 
            total=len(rgb_paths)
        ):
            try:
                # 推理
                fused_ms, _ = inference_engine.inference_single(rgb_path, s2_paths)
                
                # 加载真值
                with rasterio.open(gt_path) as src:
                    gt_data = src.read().astype(np.float32)
                    gt_data = inference_engine.s2_processor.normalize(gt_data)
                
                # 调整尺寸匹配
                if gt_data.shape != fused_ms.shape:
                    # 使用双线性插值调整尺寸
                    gt_data_resized = np.zeros_like(fused_ms)
                    for i in range(gt_data.shape[0]):
                        gt_data_resized[i] = cv2.resize(
                            gt_data[i], 
                            (fused_ms.shape[2], fused_ms.shape[1]), 
                            interpolation=cv2.INTER_LINEAR
                        )
                    gt_data = gt_data_resized
                
                # 数据范围调整
                if fused_ms.min() >= -1:
                    fused_ms = (fused_ms + 1) / 2  # [-1,1] -> [0,1]
                
                # 计算指标
                metrics = self.calculate_metrics(fused_ms, gt_data)
                all_metrics.append(metrics)
                
            except Exception as e:
                print(f"Error evaluating {rgb_path}: {e}")
                continue
        
        # 计算平均指标
        if not all_metrics:
            return {}
        
        avg_metrics = {}
        for key in all_metrics[0].keys():
            values = [m[key] for m in all_metrics if not np.isnan(m[key])]
            avg_metrics[key] = np.mean(values) if values else 0.0
            avg_metrics[f'{key}_std'] = np.std(values) if values else 0.0
        
        return avg_metrics

def main():
    parser = argparse.ArgumentParser(description='UAV-Sentinel2 Fusion Inference')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--rgb_path', type=str, help='Path to RGB image')
    parser.add_argument('--s2_paths', type=str, nargs='+', help='Paths to Sentinel-2 images')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory')
    parser.add_argument('--batch_mode', action='store_true', help='Batch processing mode')
    parser.add_argument('--rgb_list', type=str, help='File containing RGB image paths')
    parser.add_argument('--s2_list', type=str, help='File containing Sentinel-2 image paths')
    parser.add_argument('--evaluate', action='store_true', help='Run evaluation')
    parser.add_argument('--gt_list', type=str, help='File containing ground truth paths for evaluation')
    
    args = parser.parse_args()
    
    # 创建推理引擎
    inference_engine = FusionInference(args.config, args.checkpoint)
    
    if args.batch_mode:
        # 批量处理模式
        with open(args.rgb_list, 'r') as f:
            rgb_paths = [line.strip() for line in f.readlines()]
        
        with open(args.s2_list, 'r') as f:
            s2_paths_list = []
            for line in f.readlines():
                paths = line.strip().split(',')
                s2_paths_list.append(paths)
        
        # 批量推理
        results = inference_engine.batch_inference(rgb_paths, s2_paths_list, args.output_dir)
        
        # 评估（如果提供了真值）
        if args.evaluate and args.gt_list:
            with open(args.gt_list, 'r') as f:
                gt_paths = [line.strip() for line in f.readlines()]
            
            evaluator = ModelEvaluator(args.config)
            metrics = evaluator.evaluate_model(inference_engine, rgb_paths, s2_paths_list, gt_paths)
            
            print("\nEvaluation Results:")
            for key, value in metrics.items():
                print(f"{key}: {value:.4f}")
            
            # 保存评估结果
            import json
            with open(Path(args.output_dir) / 'evaluation_results.json', 'w') as f:
                json.dump(metrics, f, indent=2)
    
    else:
        # 单个图像处理模式
        if not args.rgb_path or not args.s2_paths:
            print("Error: rgb_path and s2_paths are required for single image mode")
            return
        
        fused_ms, sr_rgb = inference_engine.inference_single(args.rgb_path, args.s2_paths)
        
        # 保存结果
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        base_name = Path(args.rgb_path).stem
        
        inference_engine.save_multispectral(
            fused_ms, 
            output_dir / f"{base_name}_fused_ms.tif"
        )
        
        if sr_rgb is not None:
            inference_engine.save_rgb(
                sr_rgb, 
                output_dir / f"{base_name}_sr_rgb.tif"
            )
        
        inference_engine.save_visualization(
            fused_ms, sr_rgb, 
            output_dir / f"{base_name}_visualization.png"
        )
        
        print(f"Results saved to {output_dir}")

if __name__ == '__main__':
    main()