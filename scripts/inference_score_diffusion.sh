#!/bin/bash

# Score Diffusion SDE时空融合推理脚本

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 配置文件和模型路径
CONFIG_PATH="configs/score_diffusion_config.yaml"
CHECKPOINT_PATH="results/score_diffusion/checkpoint_best.pth"

# 推理参数
SAMPLING_STEPS=500
OUTPUT_DIR="inference_results/score_diffusion"

echo "========================================="
echo "Score Diffusion SDE 时空融合推理"
echo "========================================="
echo "配置文件: $CONFIG_PATH"
echo "模型检查点: $CHECKPOINT_PATH"
echo "采样步数: $SAMPLING_STEPS"
echo "输出目录: $OUTPUT_DIR"
echo "========================================="

# 检查文件是否存在
if [ ! -f "$CONFIG_PATH" ]; then
    echo "错误: 配置文件不存在 $CONFIG_PATH"
    exit 1
fi

if [ ! -f "$CHECKPOINT_PATH" ]; then
    echo "错误: 模型检查点不存在 $CHECKPOINT_PATH"
    exit 1
fi

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 单张图像推理示例
if [ "$1" = "single" ]; then
    RGB_PATH=$2
    TEMPORAL_MS_PATHS=$3  # 可选，多个路径用空格分隔
    
    if [ -z "$RGB_PATH" ]; then
        echo "用法: $0 single <RGB图像路径> [时间序列多光谱路径...]"
        exit 1
    fi
    
    echo "单张图像推理..."
    echo "RGB图像: $RGB_PATH"
    
    python scripts/inference_score_diffusion.py \
        --config $CONFIG_PATH \
        --checkpoint $CHECKPOINT_PATH \
        --rgb_path $RGB_PATH \
        --temporal_ms_paths $TEMPORAL_MS_PATHS \
        --sampling_steps $SAMPLING_STEPS \
        --output_dir $OUTPUT_DIR

# 批量推理示例
elif [ "$1" = "batch" ]; then
    RGB_LIST=$2
    TEMPORAL_MS_LIST=$3  # 可选
    
    if [ -z "$RGB_LIST" ]; then
        echo "用法: $0 batch <RGB列表文件> [时间序列多光谱列表文件]"
        exit 1
    fi
    
    echo "批量推理..."
    echo "RGB列表: $RGB_LIST"
    echo "时间序列列表: $TEMPORAL_MS_LIST"
    
    python scripts/inference_score_diffusion.py \
        --config $CONFIG_PATH \
        --checkpoint $CHECKPOINT_PATH \
        --rgb_list $RGB_LIST \
        --temporal_ms_list $TEMPORAL_MS_LIST \
        --batch_mode \
        --sampling_steps $SAMPLING_STEPS \
        --output_dir $OUTPUT_DIR

# 显示用法
else
    echo "用法:"
    echo "  单张推理: $0 single <RGB图像路径> [时间序列多光谱路径...]"
    echo "  批量推理: $0 batch <RGB列表文件> [时间序列多光谱列表文件]"
    echo ""
    echo "示例:"
    echo "  $0 single data/test/rgb/sample.tif data/test/sentinel2/sample_t1.tif data/test/sentinel2/sample_t2.tif"
    echo "  $0 batch data/test_rgb_list.txt data/test_temporal_ms_list.txt"
    exit 1
fi

echo "========================================="
echo "推理完成!"
echo "结果保存在: $OUTPUT_DIR"
echo "========================================"