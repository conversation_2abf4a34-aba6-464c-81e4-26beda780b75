#!/usr/bin/env python3
"""
Score Diffusion SDE时空融合系统测试脚本
"""

import torch
import numpy as np
import yaml
from pathlib import Path
import sys

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from models.enhanced_score_diffusion import EnhancedScoreDiffusionFusion, ScoreDiffusionTrainingWrapper
from models.score_sde_fusion import ScoreDiffusionSpatiotemporalFusion, VarianceExplodingSDE
from models.score_sde_losses import ScoreDiffusionComprehensiveLoss

def test_score_network():
    """测试Score网络"""
    print("🧪 测试Score网络...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = ScoreDiffusionSpatiotemporalFusion(
        rgb_channels=3,
        ms_channels=4,
        base_channels=32,  # 减小以便测试
        num_timesteps=100
    ).to(device)
    
    # 创建测试数据
    batch_size = 2
    H, W = 64, 64
    
    rgb_lr = torch.randn(batch_size, 3, H, W).to(device)
    ms_hr = torch.randn(batch_size, 4, H, W).to(device)
    
    print(f"  输入形状: RGB {rgb_lr.shape}, MS {ms_hr.shape}")
    
    # 测试训练模式
    model.train()
    loss = model(rgb_lr, ms_hr)
    print(f"  训练损失: {loss.item():.4f}")
    
    # 测试推理模式
    model.eval()
    with torch.no_grad():
        output = model(rgb_lr, sampling_method="euler_maruyama")
    print(f"  推理输出形状: {output.shape}")
    
    print("✅ Score网络测试通过")
    return True

def test_sde_components():
    """测试SDE组件"""
    print("🧪 测试SDE组件...")
    
    sde = VarianceExplodingSDE(sigma_min=0.01, sigma_max=10.0, num_scales=100)
    
    # 测试边际概率参数
    x = torch.randn(2, 4, 32, 32)
    t = torch.rand(2, 1)
    
    mean, std = sde.marginal_prob(x, t)
    print(f"  边际概率 - mean: {mean.shape}, std: {std.shape}")
    
    # 测试先验采样
    prior_sample = sde.prior_sampling((2, 4, 32, 32))
    print(f"  先验采样形状: {prior_sample.shape}")
    
    # 测试SDE系数
    drift, diffusion = sde.sde(x, t)
    print(f"  SDE系数 - drift: {drift.shape}, diffusion: {diffusion}")
    
    print("✅ SDE组件测试通过")
    return True

def test_enhanced_fusion():
    """测试增强融合系统"""
    print("🧪 测试增强融合系统...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = EnhancedScoreDiffusionFusion(
        rgb_channels=3,
        ms_channels=4,
        base_channels=32,
        num_timesteps=100,
        use_temporal_diffusion=True,
        use_hybrid_architecture=True
    ).to(device)
    
    # 测试数据
    batch_size = 1
    H, W = 64, 64
    T = 4  # 时间步数
    
    rgb_lr = torch.randn(batch_size, 3, H, W).to(device)
    ms_hr = torch.randn(batch_size, 4, H, W).to(device)
    temporal_ms = torch.randn(batch_size, T, 4, H, W).to(device)
    
    print(f"  输入形状: RGB {rgb_lr.shape}, MS {ms_hr.shape}, Temporal {temporal_ms.shape}")
    
    # 测试训练模式
    model.train()
    train_outputs = model(rgb_lr, ms_hr, temporal_ms, mode="train")
    print(f"  训练输出键: {list(train_outputs.keys())}")
    
    # 测试推理模式
    model.eval()
    with torch.no_grad():
        infer_outputs = model(rgb_lr, None, temporal_ms, mode="inference")
    print(f"  推理输出键: {list(infer_outputs.keys())}")
    print(f"  最终输出形状: {infer_outputs['output'].shape}")
    
    print("✅ 增强融合系统测试通过")
    return True

def test_training_wrapper():
    """测试训练包装器"""
    print("🧪 测试训练包装器...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建融合模型
    fusion_model = EnhancedScoreDiffusionFusion(
        rgb_channels=3,
        ms_channels=4,
        base_channels=32,
        num_timesteps=50,
        use_temporal_diffusion=False,  # 简化测试
        use_hybrid_architecture=False
    ).to(device)
    
    # 创建训练包装器
    wrapper = ScoreDiffusionTrainingWrapper(fusion_model).to(device)
    
    # 测试数据批次
    batch = {
        'rgb_lr': torch.randn(2, 3, 64, 64).to(device),
        'ms_hr': torch.randn(2, 4, 64, 64).to(device),
        'temporal_ms': None
    }
    
    # 测试训练步骤
    wrapper.train()
    losses = wrapper.training_step(batch)
    print(f"  训练损失: {losses}")
    
    # 测试验证步骤
    wrapper.eval()
    metrics = wrapper.validation_step(batch)
    print(f"  验证指标: {metrics}")
    
    print("✅ 训练包装器测试通过")
    return True

def test_loss_functions():
    """测试损失函数"""
    print("🧪 测试损失函数...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建损失函数
    criterion = ScoreDiffusionComprehensiveLoss().to(device)
    
    # 测试数据
    batch_size = 2
    predicted_score = torch.randn(batch_size, 4, 32, 32).to(device)
    target_score = torch.randn(batch_size, 4, 32, 32).to(device)
    rgb_feat = torch.randn(batch_size, 4, 32, 32).to(device)
    ms_feat = torch.randn(batch_size, 4, 32, 32).to(device)
    temporal_features = torch.randn(batch_size, 3, 4, 32, 32).to(device)
    
    # 计算损失
    losses = criterion(
        predicted_score=predicted_score,
        target_score=target_score,
        rgb_feat=rgb_feat,
        ms_feat=ms_feat,
        temporal_features=temporal_features
    )
    
    print(f"  损失项: {list(losses.keys())}")
    for key, value in losses.items():
        print(f"    {key}: {value.item():.4f}")
    
    print("✅ 损失函数测试通过")
    return True

def test_config_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载...")
    
    config_path = Path(__file__).parent.parent / "configs" / "score_diffusion_config.yaml"
    
    if not config_path.exists():
        print(f"  ❌ 配置文件不存在: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # 检查关键配置项
    required_keys = ['data', 'model', 'training', 'inference']
    for key in required_keys:
        if key not in config:
            print(f"  ❌ 缺少配置项: {key}")
            return False
    
    print(f"  ✅ 配置文件加载成功")
    print(f"    模型通道数: RGB={config['model']['rgb_channels']}, MS={config['model']['ms_channels']}")
    print(f"    扩散步数: {config['model']['num_timesteps']}")
    print(f"    批次大小: {config['training']['batch_size']}")
    
    print("✅ 配置文件测试通过")
    return True

def main():
    """主测试函数"""
    print("🚀 Score Diffusion SDE时空融合系统测试")
    print("=" * 60)
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"✅ CUDA可用: {torch.cuda.get_device_name()}")
        print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("⚠️  CUDA不可用，使用CPU测试")
    
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("配置文件加载", test_config_loading),
        ("SDE组件", test_sde_components),
        ("Score网络", test_score_network),
        ("损失函数", test_loss_functions),
        ("增强融合系统", test_enhanced_fusion),
        ("训练包装器", test_training_wrapper),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Score Diffusion SDE系统可以正常运行")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)