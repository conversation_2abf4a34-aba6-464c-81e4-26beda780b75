#!/bin/bash

# UAV-Sentinel2 时空融合训练脚本

echo "开始训练 UAV-Sentinel2 时空融合模型..."

# 设置GPU
export CUDA_VISIBLE_DEVICES=0

# 基本配置
CONFIG_FILE="configs/config.yaml"
OUTPUT_DIR="results/$(date +%Y%m%d_%H%M%S)"

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 复制配置文件到输出目录
cp $CONFIG_FILE $OUTPUT_DIR/

# 开始训练
python scripts/train.py \
    --config $CONFIG_FILE \
    > $OUTPUT_DIR/training.log 2>&1

echo "训练完成，结果保存在: $OUTPUT_DIR"

# 可选：训练完成后自动进行推理测试
# python scripts/inference.py \
#     --config $CONFIG_FILE \
#     --checkpoint $OUTPUT_DIR/checkpoint_best.pth \
#     --batch_mode \
#     --rgb_list data/test_rgb_list.txt \
#     --s2_list data/test_s2_list.txt \
#     --output_dir $OUTPUT_DIR/inference_results \
#     --evaluate \
#     --gt_list data/test_gt_list.txt