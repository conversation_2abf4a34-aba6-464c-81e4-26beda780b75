#!/bin/bash

# Score Diffusion SDE时空融合训练脚本

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 配置文件路径
CONFIG_PATH="configs/score_diffusion_config.yaml"

# 训练参数
BATCH_SIZE=4
NUM_EPOCHS=300
NUM_WORKERS=4

echo "========================================="
echo "Score Diffusion SDE 时空融合训练"
echo "========================================="
echo "配置文件: $CONFIG_PATH"
echo "批次大小: $BATCH_SIZE"
echo "训练轮数: $NUM_EPOCHS"
echo "工作进程: $NUM_WORKERS"
echo "========================================="

# 检查GPU可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"

# 开始训练
python scripts/train_score_diffusion.py \
    --config $CONFIG_PATH \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --num_workers $NUM_WORKERS

echo "========================================="
echo "训练完成!"
echo "结果保存在: results/score_diffusion/"
echo "========================================="