import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import yaml
import argparse
import os
import time
from pathlib import Path
import numpy as np
from tqdm import tqdm
from typing import Dict, Tuple
import matplotlib.pyplot as plt

import sys
sys.path.append('..')

from models.enhanced_score_diffusion import EnhancedScoreDiffusionFusion, ScoreDiffusionTrainingWrapper
from utils.data_processor import FusionDataset

class ScoreDiffusionTrainer:
    """Score Diffusion SDE训练器"""
    
    def __init__(self, config_path: str):
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # 创建输出目录
        self.output_dir = Path(self.config['training']['output_dir']) / 'score_diffusion'
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存配置
        with open(self.output_dir / 'config.yaml', 'w') as f:
            yaml.dump(self.config, f)
        
        # 初始化网络
        self._init_networks()
        
        # 初始化优化器和调度器
        self._init_optimizers()
        
        # 初始化数据加载器
        self._init_dataloaders()
        
        # 初始化记录器
        self.writer = SummaryWriter(self.output_dir / 'logs')
        
        # 训练状态
        self.current_epoch = 0
        self.best_psnr = 0.0
        self.global_step = 0
        
    def _init_networks(self):
        """初始化网络"""
        model_config = self.config['model']
        
        # 创建Score Diffusion融合模型
        self.fusion_model = EnhancedScoreDiffusionFusion(
            rgb_channels=model_config['rgb_channels'],
            ms_channels=model_config['ms_channels'],
            base_channels=model_config['base_channels'],
            num_timesteps=model_config.get('num_timesteps', 1000),
            use_temporal_diffusion=model_config.get('use_temporal_diffusion', True),
            use_hybrid_architecture=model_config.get('use_hybrid_architecture', True)
        ).to(self.device)
        
        # 训练包装器
        self.training_wrapper = ScoreDiffusionTrainingWrapper(self.fusion_model).to(self.device)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in self.fusion_model.parameters())
        trainable_params = sum(p.numel() for p in self.fusion_model.parameters() if p.requires_grad)
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        
    def _init_optimizers(self):
        """初始化优化器"""
        training_config = self.config['training']
        
        # 为不同模块使用不同的学习率
        param_groups = []
        
        # Score网络参数
        if hasattr(self.fusion_model, 'score_diffusion'):
            param_groups.append({
                'params': self.fusion_model.score_diffusion.parameters(),
                'lr': training_config['score_lr']
            })
        
        # GAN参数（如果使用混合架构）
        if hasattr(self.fusion_model, 'gan_generator'):
            param_groups.append({
                'params': self.fusion_model.gan_generator.parameters(),
                'lr': training_config['gan_lr']
            })
        
        # 其他参数
        other_params = []
        for name, param in self.fusion_model.named_parameters():
            if not any(name.startswith(prefix) for prefix in ['score_diffusion', 'gan_generator']):
                other_params.append(param)
        
        if other_params:
            param_groups.append({
                'params': other_params,
                'lr': training_config['base_lr']
            })
        
        # 优化器
        self.optimizer = optim.AdamW(
            param_groups,
            betas=(training_config['beta1'], training_config['beta2']),
            weight_decay=training_config.get('weight_decay', 1e-4)
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=self.config['training']['num_epochs'],
            eta_min=training_config.get('min_lr', 1e-6)
        )
        
    def _init_dataloaders(self):
        """初始化数据加载器"""
        data_config = self.config['data']
        training_config = self.config['training']
        
        # 训练数据集
        self.train_dataset = FusionDataset(
            data_dir=data_config['train_dir'],
            image_size=data_config['image_size'],
            mode='train'
        )
        
        self.train_loader = DataLoader(
            self.train_dataset,
            batch_size=training_config['batch_size'],
            shuffle=True,
            num_workers=training_config['num_workers'],
            pin_memory=True,
            drop_last=True
        )
        
        # 验证数据集
        self.val_dataset = FusionDataset(
            data_dir=data_config['val_dir'],
            image_size=data_config['image_size'],
            mode='val'
        )
        
        self.val_loader = DataLoader(
            self.val_dataset,
            batch_size=training_config['val_batch_size'],
            shuffle=False,
            num_workers=training_config['num_workers'],
            pin_memory=True
        )
        
    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.fusion_model.train()
        
        epoch_losses = {}
        num_batches = len(self.train_loader)
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch_idx, batch in enumerate(progress_bar):
            # 移动数据到设备
            batch = {k: v.to(self.device) if torch.is_tensor(v) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            self.optimizer.zero_grad()
            
            losses = self.training_wrapper.training_step(batch)
            
            # 反向传播
            losses['total'].backward()
            
            # 梯度裁剪
            if self.config['training'].get('grad_clip', 0) > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.fusion_model.parameters(),
                    self.config['training']['grad_clip']
                )
            
            self.optimizer.step()
            
            # 记录损失
            for key, value in losses.items():
                if key not in epoch_losses:
                    epoch_losses[key] = []
                epoch_losses[key].append(value.item())
            
            # 更新进度条
            progress_bar.set_postfix({
                'total_loss': losses['total'].item(),
                'score_loss': losses.get('score_diffusion', torch.tensor(0)).item()
            })
            
            # 记录到tensorboard
            if batch_idx % self.config['training']['log_interval'] == 0:
                for key, value in losses.items():
                    self.writer.add_scalar(f'train/{key}', value.item(), self.global_step)
                
                # 记录学习率
                for i, param_group in enumerate(self.optimizer.param_groups):
                    self.writer.add_scalar(f'lr/group_{i}', param_group['lr'], self.global_step)
            
            self.global_step += 1
        
        # 计算平均损失
        avg_losses = {key: np.mean(values) for key, values in epoch_losses.items()}
        
        return avg_losses
    
    def validate(self) -> Dict[str, float]:
        """验证"""
        self.fusion_model.eval()
        
        val_metrics = {}
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(self.val_loader, desc='Validating')):
                # 移动数据到设备
                batch = {k: v.to(self.device) if torch.is_tensor(v) else v 
                        for k, v in batch.items()}
                
                # 前向传播
                metrics = self.training_wrapper.validation_step(batch)
                
                # 记录指标
                for key, value in metrics.items():
                    if key != 'prediction':  # 跳过预测结果
                        if key not in val_metrics:
                            val_metrics[key] = []
                        val_metrics[key].append(value.item())
        
        # 计算平均指标
        avg_metrics = {key: np.mean(values) for key, values in val_metrics.items()}
        
        return avg_metrics
    
    def save_checkpoint(self, is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.fusion_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_psnr': self.best_psnr,
            'global_step': self.global_step,
            'config': self.config
        }
        
        # 保存最新检查点
        torch.save(checkpoint, self.output_dir / 'checkpoint_latest.pth')
        
        # 保存最佳检查点
        if is_best:
            torch.save(checkpoint, self.output_dir / 'checkpoint_best.pth')
        
        # 定期保存
        if self.current_epoch % self.config['training']['save_interval'] == 0:
            torch.save(checkpoint, self.output_dir / f'checkpoint_epoch_{self.current_epoch}.pth')
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.fusion_model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_psnr = checkpoint['best_psnr']
        self.global_step = checkpoint['global_step']
        
        print(f"Loaded checkpoint from epoch {self.current_epoch}")
    
    def train(self):
        """完整训练流程"""
        print("Starting Score Diffusion SDE training...")
        print(f"Total epochs: {self.config['training']['num_epochs']}")
        
        for epoch in range(self.current_epoch, self.config['training']['num_epochs']):
            self.current_epoch = epoch
            
            # 训练
            train_losses = self.train_epoch()
            
            # 验证
            val_metrics = self.validate()
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录到tensorboard
            for key, value in train_losses.items():
                self.writer.add_scalar(f'epoch_train/{key}', value, epoch)
            
            for key, value in val_metrics.items():
                self.writer.add_scalar(f'epoch_val/{key}', value, epoch)
            
            # 检查是否是最佳模型
            current_psnr = val_metrics.get('psnr', 0)
            is_best = current_psnr > self.best_psnr
            if is_best:
                self.best_psnr = current_psnr
            
            # 保存检查点
            self.save_checkpoint(is_best)
            
            # 打印信息
            print(f"Epoch {epoch}: "
                  f"Train Loss: {train_losses['total']:.4f}, "
                  f"Val PSNR: {current_psnr:.2f}, "
                  f"Best PSNR: {self.best_psnr:.2f}")
        
        print("Training completed!")
        self.writer.close()

def main():
    parser = argparse.ArgumentParser(description='Score Diffusion SDE Training')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = ScoreDiffusionTrainer(args.config)
    
    # 恢复训练（如果指定）
    if args.resume:
        trainer.load_checkpoint(args.resume)
    
    # 开始训练
    trainer.train()

if __name__ == '__main__':
    main()