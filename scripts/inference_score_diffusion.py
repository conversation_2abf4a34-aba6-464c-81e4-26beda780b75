import torch
import torch.nn.functional as F
import numpy as np
import argparse
import yaml
from pathlib import Path
import cv2
import rasterio
from typing import Dict, List, Optional
import matplotlib.pyplot as plt

import sys
sys.path.append('..')

from models.enhanced_score_diffusion import EnhancedScoreDiffusionFusion
from utils.data_processor import RGBProcessor, MSProcessor

class ScoreDiffusionInference:
    """Score Diffusion SDE推理器"""
    
    def __init__(self, config_path: str, checkpoint_path: str):
        # 加载配置
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # 设备配置
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # 初始化模型
        self._init_model(checkpoint_path)
        
        # 初始化数据处理器
        self.rgb_processor = RGBProcessor(tuple(self.config['data']['image_size']))
        self.ms_processor = MSProcessor(tuple(self.config['data']['image_size']))
        
    def _init_model(self, checkpoint_path: str):
        """初始化模型"""
        model_config = self.config['model']
        
        # 创建模型
        self.model = EnhancedScoreDiffusionFusion(
            rgb_channels=model_config['rgb_channels'],
            ms_channels=model_config['ms_channels'],
            base_channels=model_config['base_channels'],
            num_timesteps=model_config.get('num_timesteps', 1000),
            use_temporal_diffusion=model_config.get('use_temporal_diffusion', True),
            use_hybrid_architecture=model_config.get('use_hybrid_architecture', True)
        ).to(self.device)
        
        # 加载权重
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        print(f"Loaded model from {checkpoint_path}")
        print(f"Best PSNR: {checkpoint.get('best_psnr', 'N/A')}")
    
    def preprocess_rgb(self, rgb_path: str) -> torch.Tensor:
        """预处理RGB图像"""
        rgb_image = self.rgb_processor.load_rgb_image(rgb_path)
        
        # 转换为tensor并添加batch维度
        rgb_tensor = torch.from_numpy(rgb_image).permute(2, 0, 1).unsqueeze(0)
        rgb_tensor = rgb_tensor.to(self.device).float()
        
        # 归一化到[-1, 1]
        rgb_tensor = rgb_tensor * 2.0 - 1.0
        
        return rgb_tensor
    
    def preprocess_temporal_ms(self, ms_paths: List[str]) -> Optional[torch.Tensor]:
        """预处理时间序列多光谱数据"""
        if not ms_paths:
            return None
        
        temporal_data = []
        for ms_path in ms_paths:
            ms_image = self.ms_processor.load_ms_image(ms_path)
            ms_tensor = torch.from_numpy(ms_image).permute(2, 0, 1)
            temporal_data.append(ms_tensor)
        
        # 拼接时间维度 [T, C, H, W]
        temporal_tensor = torch.stack(temporal_data, dim=0)
        
        # 添加batch维度 [1, T, C, H, W]
        temporal_tensor = temporal_tensor.unsqueeze(0).to(self.device).float()
        
        # 归一化
        temporal_tensor = temporal_tensor * 2.0 - 1.0
        
        return temporal_tensor
    
    def postprocess_output(self, output: torch.Tensor) -> np.ndarray:
        """后处理输出"""
        # 反归一化
        output = (output + 1.0) / 2.0
        
        # 裁剪到[0,1]
        output = torch.clamp(output, 0, 1)
        
        # 转换为numpy
        output_np = output.squeeze(0).cpu().numpy()
        output_np = np.transpose(output_np, (1, 2, 0))
        
        return output_np
    
    def single_inference(self, 
                        rgb_path: str, 
                        temporal_ms_paths: Optional[List[str]] = None,
                        sampling_steps: int = 500) -> Dict[str, np.ndarray]:
        """单张图像推理"""
        # 预处理输入
        rgb_tensor = self.preprocess_rgb(rgb_path)
        temporal_ms_tensor = self.preprocess_temporal_ms(temporal_ms_paths) if temporal_ms_paths else None
        
        # 推理
        with torch.no_grad():
            # 临时修改采样步数（如果模型支持）
            if hasattr(self.model.score_diffusion, 'predictor_corrector_sampler'):
                original_forward = self.model.forward
                
                def custom_forward(rgb_lr, ms_hr=None, temporal_ms=None, mode="inference"):
                    results = {}
                    if mode == "inference":
                        diffusion_output = self.model.score_diffusion.predictor_corrector_sampler(
                            rgb_lr, num_steps=sampling_steps
                        )
                        
                        if self.model.use_hybrid_architecture:
                            with torch.no_grad():
                                gan_output, _ = self.model.gan_generator(
                                    rgb_lr, 
                                    F.interpolate(rgb_lr, size=diffusion_output.shape[-2:], 
                                                mode='bilinear', align_corners=False).repeat(1, self.model.ms_channels//self.model.rgb_channels + 1, 1, 1)[:, :self.model.ms_channels]
                                )
                            
                            fused_features = self.model.feature_fusion(torch.cat([gan_output, diffusion_output], dim=1))
                            multiscale_features = self.model.extract_multiscale_features(fused_features)
                            attended_features = self.model.apply_multiscale_attention(multiscale_features)
                            fusion_weights = self.model.adaptive_fusion(attended_features)
                            
                            final_output = fused_features * fusion_weights + diffusion_output * (1 - fusion_weights)
                            
                            results['output'] = final_output
                            results['diffusion_output'] = diffusion_output
                            results['gan_output'] = gan_output
                            results['fusion_weights'] = fusion_weights
                        else:
                            results['output'] = diffusion_output
                        
                        if self.model.use_temporal_diffusion and temporal_ms is not None:
                            B, T, C, H, W = temporal_ms.shape
                            temporal_features = temporal_ms.view(B, T, C, H, W)
                            temporal_enhanced = self.model.temporal_diffusion(temporal_features)
                            
                            if 'output' in results:
                                temporal_weight = 0.3
                                results['output'] = (1 - temporal_weight) * results['output'] + temporal_weight * temporal_enhanced
                            
                            results['temporal_enhanced'] = temporal_enhanced
                    
                    return results
                
                self.model.forward = custom_forward
            
            outputs = self.model(rgb_tensor, None, temporal_ms_tensor, mode="inference")
        
        # 后处理结果
        results = {}
        for key, value in outputs.items():
            if torch.is_tensor(value) and value.dim() == 4:  # [B, C, H, W]
                results[key] = self.postprocess_output(value)
        
        return results
    
    def batch_inference(self, 
                       rgb_list_path: str, 
                       temporal_ms_list_path: Optional[str] = None,
                       output_dir: str = "inference_results") -> None:
        """批量推理"""
        # 读取文件列表
        with open(rgb_list_path, 'r') as f:
            rgb_paths = [line.strip() for line in f.readlines()]
        
        temporal_ms_lists = None
        if temporal_ms_list_path:
            with open(temporal_ms_list_path, 'r') as f:
                temporal_ms_lists = [line.strip().split(',') for line in f.readlines()]
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Processing {len(rgb_paths)} images...")
        
        for i, rgb_path in enumerate(rgb_paths):
            print(f"Processing {i+1}/{len(rgb_paths)}: {rgb_path}")
            
            # 获取对应的时间序列数据
            temporal_ms_paths = temporal_ms_lists[i] if temporal_ms_lists else None
            
            try:
                # 推理
                results = self.single_inference(rgb_path, temporal_ms_paths)
                
                # 保存结果
                base_name = Path(rgb_path).stem
                
                for key, output in results.items():
                    if key == 'output':
                        # 主要输出
                        output_file = output_path / f"{base_name}_fused.tif"
                        self.save_multispectral_image(output, str(output_file))
                        
                        # 保存RGB可视化
                        if output.shape[-1] >= 3:
                            rgb_vis = output[:, :, :3]
                            plt.figure(figsize=(10, 10))
                            plt.imshow(rgb_vis)
                            plt.axis('off')
                            plt.title(f'Fused Result: {base_name}')
                            plt.savefig(output_path / f"{base_name}_fused_rgb.png", 
                                      bbox_inches='tight', dpi=150)
                            plt.close()
                    
                    elif key in ['diffusion_output', 'gan_output']:
                        # 中间结果
                        output_file = output_path / f"{base_name}_{key}.tif"
                        self.save_multispectral_image(output, str(output_file))
                
            except Exception as e:
                print(f"Error processing {rgb_path}: {str(e)}")
                continue
        
        print(f"Batch inference completed. Results saved to {output_path}")
    
    def save_multispectral_image(self, image: np.ndarray, output_path: str):
        """保存多光谱图像"""
        # image shape: [H, W, C]
        H, W, C = image.shape
        
        # 转换为rasterio格式 [C, H, W]
        image_rasterio = np.transpose(image, (2, 0, 1))
        
        # 保存为GeoTIFF
        with rasterio.open(
            output_path, 'w',
            driver='GTiff',
            height=H, width=W,
            count=C,
            dtype=image.dtype,
            crs=None,  # 可以根据需要设置坐标系
            transform=None
        ) as dst:
            dst.write(image_rasterio)
    
    def compare_with_ground_truth(self, 
                                rgb_path: str, 
                                gt_path: str, 
                                temporal_ms_paths: Optional[List[str]] = None) -> Dict[str, float]:
        """与真值对比"""
        # 推理
        results = self.single_inference(rgb_path, temporal_ms_paths)
        
        # 加载真值
        gt_image = self.ms_processor.load_ms_image(gt_path)
        
        # 计算指标
        metrics = {}
        
        if 'output' in results:
            pred = results['output']
            
            # PSNR
            mse = np.mean((pred - gt_image) ** 2)
            psnr = 20 * np.log10(1.0 / np.sqrt(mse))
            metrics['psnr'] = psnr
            
            # SSIM (简化版本)
            metrics['ssim'] = self.compute_ssim_np(pred, gt_image)
            
            # MAE
            metrics['mae'] = np.mean(np.abs(pred - gt_image))
            
            # RMSE
            metrics['rmse'] = np.sqrt(mse)
        
        return metrics
    
    def compute_ssim_np(self, pred: np.ndarray, target: np.ndarray) -> float:
        """计算SSIM (numpy版本)"""
        # 简化的SSIM计算，针对多通道图像
        ssim_values = []
        
        for c in range(pred.shape[-1]):
            pred_c = pred[:, :, c]
            target_c = target[:, :, c]
            
            mu1 = np.mean(pred_c)
            mu2 = np.mean(target_c)
            
            sigma1_sq = np.var(pred_c)
            sigma2_sq = np.var(target_c)
            sigma12 = np.mean((pred_c - mu1) * (target_c - mu2))
            
            C1 = 0.01 ** 2
            C2 = 0.03 ** 2
            
            ssim = ((2 * mu1 * mu2 + C1) * (2 * sigma12 + C2)) / \
                   ((mu1**2 + mu2**2 + C1) * (sigma1_sq + sigma2_sq + C2))
            
            ssim_values.append(ssim)
        
        return np.mean(ssim_values)

def main():
    parser = argparse.ArgumentParser(description='Score Diffusion SDE Inference')
    parser.add_argument('--config', type=str, required=True, help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--rgb_path', type=str, help='RGB图像路径')
    parser.add_argument('--temporal_ms_paths', type=str, nargs='+', help='时间序列多光谱图像路径')
    parser.add_argument('--rgb_list', type=str, help='RGB图像列表文件')
    parser.add_argument('--temporal_ms_list', type=str, help='时间序列多光谱列表文件')
    parser.add_argument('--output_dir', type=str, default='inference_results', help='输出目录')
    parser.add_argument('--batch_mode', action='store_true', help='批量推理模式')
    parser.add_argument('--sampling_steps', type=int, default=500, help='扩散采样步数')
    parser.add_argument('--gt_path', type=str, help='真值图像路径（用于评估）')
    
    args = parser.parse_args()
    
    # 创建推理器
    inferencer = ScoreDiffusionInference(args.config, args.checkpoint)
    
    if args.batch_mode:
        # 批量推理
        if not args.rgb_list:
            raise ValueError("批量模式需要提供RGB图像列表文件")
        
        inferencer.batch_inference(
            args.rgb_list, 
            args.temporal_ms_list, 
            args.output_dir
        )
    
    else:
        # 单张推理
        if not args.rgb_path:
            raise ValueError("单张推理模式需要提供RGB图像路径")
        
        results = inferencer.single_inference(
            args.rgb_path, 
            args.temporal_ms_paths,
            args.sampling_steps
        )
        
        # 保存结果
        output_path = Path(args.output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        base_name = Path(args.rgb_path).stem
        
        for key, output in results.items():
            if key == 'output':
                output_file = output_path / f"{base_name}_score_diffusion_fused.tif"
                inferencer.save_multispectral_image(output, str(output_file))
                print(f"Saved main result to {output_file}")
        
        # 如果提供了真值，计算指标
        if args.gt_path:
            metrics = inferencer.compare_with_ground_truth(
                args.rgb_path, args.gt_path, args.temporal_ms_paths
            )
            print("Evaluation metrics:")
            for key, value in metrics.items():
                print(f"  {key.upper()}: {value:.4f}")

if __name__ == '__main__':
    main()