import numpy as np
import cv2
import rasterio
from rasterio.enums import Resampling
import torch
from torch.utils.data import Dataset
from torchvision import transforms
import albumentations as A
from pathlib import Path
from typing import Tuple, List, Dict, Optional
import yaml

class ImageProcessor:
    """影像预处理基类"""
    
    def __init__(self, target_size: Tuple[int, int] = (256, 256)):
        self.target_size = target_size
        
    def normalize(self, image: np.ndarray, min_val: float = 0.0, max_val: float = 1.0) -> np.ndarray:
        """归一化到指定范围"""
        img_min, img_max = image.min(), image.max()
        normalized = (image - img_min) / (img_max - img_min)
        return normalized * (max_val - min_val) + min_val
    
    def resize_image(self, image: np.ndarray, size: Tuple[int, int]) -> np.ndarray:
        """调整图像尺寸"""
        if len(image.shape) == 3:
            return cv2.resize(image, size, interpolation=cv2.INTER_CUBIC)
        else:
            resized_bands = []
            for i in range(image.shape[0]):
                band = cv2.resize(image[i], size, interpolation=cv2.INTER_CUBIC)
                resized_bands.append(band)
            return np.stack(resized_bands, axis=0)

class RGBProcessor(ImageProcessor):
    """RGB影像处理器"""
    
    def __init__(self, target_size: Tuple[int, int] = (256, 256)):
        super().__init__(target_size)
        
    def load_rgb_image(self, file_path: str) -> np.ndarray:
        """加载RGB影像"""
        with rasterio.open(file_path) as src:
            # 读取RGB波段 (通常是波段1,2,3)
            rgb = src.read([1, 2, 3]).astype(np.float32)
            # 转换为HWC格式
            rgb = np.transpose(rgb, (1, 2, 0))
            return self.normalize(rgb)
    
    def preprocess_rgb(self, rgb_image: np.ndarray) -> torch.Tensor:
        """预处理RGB影像"""
        # 调整尺寸
        rgb_resized = self.resize_image(rgb_image, self.target_size)
        
        # 数据增强
        transform = A.Compose([
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),
            A.Rotate(limit=30, p=0.5),
            A.RandomBrightnessContrast(p=0.2),
        ])
        
        augmented = transform(image=rgb_resized)
        rgb_tensor = torch.from_numpy(augmented['image']).permute(2, 0, 1).float()
        return rgb_tensor

class Sentinel2Processor(ImageProcessor):
    """Sentinel-2多光谱影像处理器"""
    
    def __init__(self, target_size: Tuple[int, int] = (256, 256), 
                 selected_bands: List[int] = [2, 3, 4, 8]):  # B,G,R,NIR
        super().__init__(target_size)
        self.selected_bands = selected_bands
        
    def load_sentinel2_image(self, file_path: str) -> np.ndarray:
        """加载Sentinel-2影像"""
        with rasterio.open(file_path) as src:
            # 读取选定波段
            bands = []
            for band_idx in self.selected_bands:
                band = src.read(band_idx).astype(np.float32)
                bands.append(band)
            
            multispectral = np.stack(bands, axis=0)
            return self.normalize(multispectral)
    
    def preprocess_sentinel2(self, s2_image: np.ndarray) -> torch.Tensor:
        """预处理Sentinel-2影像"""
        # 调整尺寸到目标分辨率
        s2_resized = self.resize_image(s2_image, self.target_size)
        
        # 转换为张量
        s2_tensor = torch.from_numpy(s2_resized).float()
        return s2_tensor
    
    def temporal_interpolation(self, s2_images: List[np.ndarray], 
                             target_time: float, times: List[float]) -> np.ndarray:
        """时间插值融合多个Sentinel-2影像"""
        if len(s2_images) == 1:
            return s2_images[0]
        
        # 找到最近的两个时间点
        times = np.array(times)
        idx = np.searchsorted(times, target_time)
        
        if idx == 0:
            return s2_images[0]
        elif idx >= len(times):
            return s2_images[-1]
        else:
            # 线性插值
            t1, t2 = times[idx-1], times[idx]
            weight = (target_time - t1) / (t2 - t1)
            
            interpolated = (1 - weight) * s2_images[idx-1] + weight * s2_images[idx]
            return interpolated

class FusionDataset(Dataset):
    """时空融合数据集"""
    
    def __init__(self, config_path: str, mode: str = 'train'):
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.mode = mode
        self.rgb_processor = RGBProcessor(
            target_size=tuple(self.config['data']['image_size'])
        )
        self.s2_processor = Sentinel2Processor(
            target_size=tuple(self.config['data']['image_size']),
            selected_bands=self.config['data']['sentinel2_bands']
        )
        
        # 加载数据路径
        self.data_pairs = self._load_data_pairs()
        
    def _load_data_pairs(self) -> List[Dict]:
        """加载数据对"""
        data_dir = Path(self.config['data'][f'{self.mode}_dir'])
        pairs = []
        
        # 假设数据组织为: RGB影像对应多个时相的Sentinel-2影像
        rgb_files = list((data_dir / 'rgb').glob('*.tif'))
        
        for rgb_file in rgb_files:
            # 查找对应的Sentinel-2时间序列
            base_name = rgb_file.stem
            s2_files = list((data_dir / 'sentinel2').glob(f'{base_name}_*.tif'))
            
            if len(s2_files) > 0:
                pairs.append({
                    'rgb_path': str(rgb_file),
                    's2_paths': [str(f) for f in s2_files],
                    'times': self._extract_times(s2_files)  # 从文件名提取时间信息
                })
        
        return pairs
    
    def _extract_times(self, s2_files: List[Path]) -> List[float]:
        """从文件名提取时间信息"""
        times = []
        for file in s2_files:
            # 假设文件名包含日期信息，如：name_20220301.tif
            try:
                date_str = file.stem.split('_')[-1]
                # 简化处理：将日期转换为天数
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                time_value = (year - 2020) * 365 + month * 30 + day
                times.append(float(time_value))
            except:
                times.append(0.0)
        return times
    
    def __len__(self) -> int:
        return len(self.data_pairs)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        pair = self.data_pairs[idx]
        
        # 加载RGB影像
        rgb_image = self.rgb_processor.load_rgb_image(pair['rgb_path'])
        rgb_tensor = self.rgb_processor.preprocess_rgb(rgb_image)
        
        # 加载和处理Sentinel-2影像
        s2_images = []
        for s2_path in pair['s2_paths']:
            s2_image = self.s2_processor.load_sentinel2_image(s2_path)
            s2_images.append(s2_image)
        
        # 时间插值（假设目标时间为RGB影像时间）
        target_time = np.mean(pair['times'])  # 简化处理
        s2_interpolated = self.s2_processor.temporal_interpolation(
            s2_images, target_time, pair['times']
        )
        s2_tensor = self.s2_processor.preprocess_sentinel2(s2_interpolated)
        
        # 创建低分辨率版本（用于训练）
        rgb_lr = torch.nn.functional.interpolate(
            rgb_tensor.unsqueeze(0), 
            scale_factor=0.25, 
            mode='bilinear', 
            align_corners=False
        ).squeeze(0)
        
        return {
            'rgb_hr': rgb_tensor,      # 高分辨率RGB
            'rgb_lr': rgb_lr,          # 低分辨率RGB
            's2_ms': s2_tensor,        # Sentinel-2多光谱
            'target_time': torch.tensor(target_time, dtype=torch.float32)
        }