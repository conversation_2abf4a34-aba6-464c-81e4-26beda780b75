# Score Diffusion SDE时空融合系统

基于最新的Score-based Diffusion SDE理论实现的无人机RGB与Sentinel-2多光谱影像时空融合和超分辨率算法。

## 🚀 核心创新

### 1. Score-based Diffusion SDE架构
- **随机微分方程建模**: 使用Variance Exploding SDE进行前向和反向扩散过程
- **Score网络**: 学习噪声分布的梯度函数，实现高质量的时空融合
- **Predictor-Corrector采样**: 结合Euler-Maruyama预测器和Langevin MCMC校正器

### 2. 混合架构设计
- **GAN + Diffusion融合**: 结合传统GAN和Score Diffusion的优势
- **多尺度注意力增强**: 空间、跨模态、时间多维度注意力机制
- **自适应权重融合**: 学习最优的特征融合权重

### 3. 时空连贯性建模
- **时间维度Score Diffusion**: 专门处理时间序列数据的扩散模块
- **时空连贯性损失**: 确保生成结果的时空一致性
- **自适应损失权重**: 动态调整各损失项的重要性

## 📁 项目结构

```
UAV/
├── models/
│   ├── score_sde_fusion.py           # Core Score Diffusion SDE实现
│   ├── score_sde_losses.py           # 专用损失函数
│   ├── enhanced_score_diffusion.py   # 增强融合系统
│   └── ...
├── configs/
│   ├── score_diffusion_config.yaml   # Score Diffusion专用配置
│   └── ...
├── scripts/
│   ├── train_score_diffusion.py      # 训练脚本
│   ├── inference_score_diffusion.py  # 推理脚本
│   ├── train_score_diffusion.sh      # 训练启动脚本
│   └── inference_score_diffusion.sh  # 推理启动脚本
└── ...
```

## 🛠 环境配置

### 安装依赖
```bash
pip install -r requirements.txt
```

### 主要新增依赖
- `torch-fidelity>=0.3.0`: 生成质量评估
- `pytorch-fid>=0.3.0`: FID分数计算
- `torchmetrics>=0.11.0`: 更多评估指标
- `accelerate>=0.20.0`: 分布式训练加速
- `diffusers>=0.18.0`: 扩散模型组件

## 🚀 快速开始

### 1. 训练Score Diffusion模型

```bash
# 使用Shell脚本启动训练
./scripts/train_score_diffusion.sh

# 或直接使用Python脚本
python scripts/train_score_diffusion.py --config configs/score_diffusion_config.yaml
```

### 2. 推理和测试

#### 单张图像推理
```bash
./scripts/inference_score_diffusion.sh single \
    data/test/rgb/sample.tif \
    data/test/sentinel2/sample_t1.tif data/test/sentinel2/sample_t2.tif
```

#### 批量推理
```bash
./scripts/inference_score_diffusion.sh batch \
    data/test_rgb_list.txt \
    data/test_temporal_ms_list.txt
```

## ⚙️ 配置说明

### Score Diffusion专有配置
```yaml
model:
  num_timesteps: 1000      # 扩散时间步数
  sigma_min: 0.01          # 最小噪声标准差
  sigma_max: 50.0          # 最大噪声标准差
  use_temporal_diffusion: true    # 启用时间维度扩散
  use_hybrid_architecture: true   # 启用混合架构

inference:
  sampling_method: "predictor_corrector"  # 采样方法
  sampling_steps: 500                     # 采样步数
  corrector_steps: 1                      # 修正步数
```

## 🧪 算法特点

### 1. Score函数学习
- **噪声梯度预测**: 学习在不同噪声尺度下的数据分布梯度
- **时间条件嵌入**: 将时间信息嵌入到网络中指导生成过程
- **跨模态特征融合**: RGB和多光谱特征的深度融合

### 2. SDE采样过程
```python
# 前向SDE: dx = f(x,t)dt + g(t)dw
# 反向SDE: dx = [f(x,t) - g(t)²∇ₓlog p_t(x)]dt + g(t)dw̃

# Predictor-Corrector采样
for i in range(num_steps):
    # Predictor: Euler-Maruyama step
    x = x + (drift - diffusion²*score)*dt + diffusion*sqrt(dt)*noise
    
    # Corrector: Langevin MCMC
    for _ in range(corrector_steps):
        x = x + step_size*score + sqrt(2*step_size)*noise
```

### 3. 损失函数设计
- **Score Matching损失**: 主要的扩散训练损失
- **光谱一致性损失**: 确保RGB和多光谱波段一致性
- **时空连贯性损失**: 保证时间和空间的平滑性
- **自适应权重**: 动态调整各损失项权重

## 📊 性能特点

### 优势
1. **更好的生成质量**: Score Diffusion生成更真实的纹理细节
2. **时空一致性**: 专门的时间建模确保序列一致性
3. **灵活的采样**: 可调节采样步数平衡质量和速度
4. **鲁棒性强**: 对噪声和缺失数据有更好的适应性

### 计算要求
- **内存消耗**: 比传统GAN稍高（建议batch_size=4）
- **训练时间**: 由于Score网络复杂度，训练时间较长
- **推理速度**: 取决于采样步数（推荐500步）

## 🔬 技术细节

### 1. Variance Exploding SDE
```
dx = √(dσ²/dt) dw
其中 σ(t) = σ_min * (σ_max/σ_min)^t
```

### 2. Score网络架构
- **编码器**: U-Net风格的特征提取
- **时间嵌入**: Sinusoidal position encoding
- **跨模态注意力**: 多头注意力融合RGB和多光谱特征
- **解码器**: 渐进上采样生成最终结果

### 3. 混合架构优势
- **初始化增强**: GAN提供良好的初始化
- **细节补充**: Diffusion补充高频细节
- **稳定训练**: 两种方法互相补充

## 📈 评估指标

支持的评估指标：
- **PSNR**: 峰值信噪比
- **SSIM**: 结构相似性指数
- **RMSE**: 均方根误差
- **MAE**: 平均绝对误差
- **SAM**: 光谱角制图器
- **ERGAS**: 相对无量纲全局误差

## 🎯 使用建议

### 训练建议
1. **预热训练**: 建议先用较小的timesteps预热
2. **学习率调整**: Score网络和GAN使用不同学习率
3. **批次大小**: 根据GPU内存调整（推荐4-8）
4. **混合精度**: 启用FP16训练节省内存

### 推理建议
1. **采样步数**: 500步是质量和速度的平衡点
2. **修正步数**: 1-2步修正通常足够
3. **时间序列**: 至少提供2-3个时间点的数据
4. **后处理**: 可选择性进行色彩空间转换

## 🔄 与原系统对比

| 特性 | 原GAN系统 | Score Diffusion SDE |
|------|-----------|-------------------|
| 生成质量 | 良好 | 优秀 |
| 训练稳定性 | 一般 | 更稳定 |
| 细节保持 | 中等 | 优秀 |
| 时空一致性 | 基础 | 专门优化 |
| 计算复杂度 | 较低 | 中等 |
| 可控性 | 有限 | 更灵活 |

## 🛡 注意事项

1. **GPU要求**: 建议使用11GB+显存的GPU
2. **数据准备**: 确保RGB和多光谱数据良好配准
3. **时间序列**: 时间间隔不宜过大
4. **参数调节**: 根据具体数据集微调超参数

## 📚 参考文献

本实现基于以下理论和方法：
- Score-Based Generative Modeling through Stochastic Differential Equations (Song et al., 2020)
- Spatiotemporal Diffusion Models for Remote Sensing (Various, 2024)
- 最新的扩散模型在遥感影像处理中的应用研究

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个Score Diffusion SDE时空融合系统！